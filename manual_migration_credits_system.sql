-- =====================================================
-- OCR AI Credits System - Complete Migration
-- =====================================================
-- Run this in your Supabase SQL Editor
-- Project: gfgygdteyrumaxifgyqg.supabase.co

BEGIN;

-- =====================================================
-- 1. CREATE TABLES
-- =====================================================

-- User Credits Table
CREATE TABLE IF NOT EXISTS public.user_credits (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  credits_remaining integer DEFAULT 15,
  total_credits_purchased integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);

-- Uploads Table  
CREATE TABLE IF NOT EXISTS public.uploads (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  filename text NOT NULL,
  file_type text NOT NULL,
  file_size integer NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'done', 'error')),
  webhook_url text,
  ai_generated_image_url text,
  ai_description text,
  credits_used integer DEFAULT 1,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Credit Transactions Table
CREATE TABLE IF NOT EXISTS public.credit_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  amount integer NOT NULL,
  transaction_type text NOT NULL CHECK (transaction_type IN ('purchase', 'usage', 'refund')),
  stripe_payment_intent_id text,
  upload_id uuid REFERENCES public.uploads(id),
  created_at timestamptz DEFAULT now()
);

-- =====================================================
-- 2. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE public.user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 3. CREATE RLS POLICIES
-- =====================================================

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can read own credits" ON public.user_credits;
DROP POLICY IF EXISTS "Users can update own credits" ON public.user_credits;
DROP POLICY IF EXISTS "Users can insert own credits" ON public.user_credits;
DROP POLICY IF EXISTS "Service role can manage credits" ON public.user_credits;

DROP POLICY IF EXISTS "Users can read own uploads" ON public.uploads;
DROP POLICY IF EXISTS "Users can insert own uploads" ON public.uploads;
DROP POLICY IF EXISTS "Users can update own uploads" ON public.uploads;
DROP POLICY IF EXISTS "Service role can manage uploads" ON public.uploads;

DROP POLICY IF EXISTS "Users can read own credit transactions" ON public.credit_transactions;
DROP POLICY IF EXISTS "Users can insert own credit transactions" ON public.credit_transactions;
DROP POLICY IF EXISTS "Service role can manage transactions" ON public.credit_transactions;

-- RLS Policies for user_credits
CREATE POLICY "Users can read own credits"
  ON public.user_credits
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own credits"
  ON public.user_credits
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own credits"
  ON public.user_credits
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service role can manage credits"
  ON public.user_credits
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- RLS Policies for uploads
CREATE POLICY "Users can read own uploads"
  ON public.uploads
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own uploads"
  ON public.uploads
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own uploads"
  ON public.uploads
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage uploads"
  ON public.uploads
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- RLS Policies for credit_transactions
CREATE POLICY "Users can read own credit transactions"
  ON public.credit_transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own credit transactions"
  ON public.credit_transactions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service role can manage transactions"
  ON public.credit_transactions
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- =====================================================
-- 4. CREATE FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically create user credits on signup
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_credits (user_id)
  VALUES (new.id);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to deduct credits and create transaction
CREATE OR REPLACE FUNCTION public.deduct_credits(
  p_user_id uuid,
  p_upload_id uuid,
  p_credits_to_deduct integer DEFAULT 1
)
RETURNS boolean AS $$
DECLARE
  current_credits integer;
BEGIN
  -- Get current credits with row lock
  SELECT credits_remaining INTO current_credits
  FROM public.user_credits 
  WHERE user_id = p_user_id
  FOR UPDATE;
  
  -- Check if user has enough credits
  IF current_credits IS NULL OR current_credits < p_credits_to_deduct THEN
    RETURN false;
  END IF;
  
  -- Deduct credits
  UPDATE public.user_credits 
  SET credits_remaining = credits_remaining - p_credits_to_deduct
  WHERE user_id = p_user_id;
  
  -- Create transaction record
  INSERT INTO public.credit_transactions (user_id, amount, transaction_type, upload_id)
  VALUES (p_user_id, -p_credits_to_deduct, 'usage', p_upload_id);
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add credits and create transaction
CREATE OR REPLACE FUNCTION public.add_credits(
  p_user_id uuid,
  p_credits_to_add integer,
  p_stripe_payment_intent_id text DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  -- Add credits
  INSERT INTO public.user_credits (user_id, credits_remaining, total_credits_purchased)
  VALUES (p_user_id, p_credits_to_add, p_credits_to_add)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    credits_remaining = public.user_credits.credits_remaining + p_credits_to_add,
    total_credits_purchased = public.user_credits.total_credits_purchased + p_credits_to_add;
  
  -- Create transaction record
  INSERT INTO public.credit_transactions (user_id, amount, transaction_type, stripe_payment_intent_id)
  VALUES (p_user_id, p_credits_to_add, 'purchase', p_stripe_payment_intent_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. CREATE TRIGGERS
-- =====================================================

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_user_credits_updated_at ON public.user_credits;
DROP TRIGGER IF EXISTS update_uploads_updated_at ON public.uploads;

-- Trigger to create user credits on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Trigger to automatically update updated_at on user_credits
CREATE TRIGGER update_user_credits_updated_at
    BEFORE UPDATE ON public.user_credits
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Trigger to automatically update updated_at on uploads
CREATE TRIGGER update_uploads_updated_at
    BEFORE UPDATE ON public.uploads
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated, service_role;

-- Grant permissions on tables
GRANT ALL ON public.user_credits TO authenticated, service_role;
GRANT ALL ON public.uploads TO authenticated, service_role;
GRANT ALL ON public.credit_transactions TO authenticated, service_role;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.deduct_credits(uuid, uuid, integer) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.add_credits(uuid, integer, text) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.update_updated_at_column() TO authenticated, service_role;

-- =====================================================
-- 7. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_credits_user_id ON public.user_credits(user_id);
CREATE INDEX IF NOT EXISTS idx_uploads_user_id ON public.uploads(user_id);
CREATE INDEX IF NOT EXISTS idx_uploads_status ON public.uploads(status);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON public.credit_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_type ON public.credit_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON public.credit_transactions(created_at);

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these after the migration to verify everything works:

-- Check if tables exist
-- SELECT table_name FROM information_schema.tables 
-- WHERE table_schema = 'public' 
-- AND table_name IN ('user_credits', 'uploads', 'credit_transactions');

-- Check if functions exist
-- SELECT routine_name FROM information_schema.routines 
-- WHERE routine_schema = 'public' 
-- AND routine_name IN ('deduct_credits', 'add_credits', 'handle_new_user');

-- Test the trigger (create a test user - this will create credits automatically)
-- INSERT INTO auth.users (id, email) VALUES (gen_random_uuid(), '<EMAIL>');
-- DELETE FROM auth.users WHERE email = '<EMAIL>';
