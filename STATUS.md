# STATUS.md

This document provides current project status, completed milestones, and the roadmap for upcoming features.

---

## 1. Completed Milestones

Milestone                                         |
------------------------------------------------- |
Initial repo setup (Next.js + Supabase)           |
File upload + Storage integration                 |
OCR Edge Function (llama‑ocr)                     |
|Prompt‑chain implementation (extract → clean)    |
 Webhook integration with n8n                     |
UI: Invoice template selection & preview          |
 Credits & Auth system (15 free credits on signup)|

---

## 2. Current Sprint (2025‑07‑01 → 2025‑07‑15)

* **PDF Export**: Implement HTML‑to‑PDF rendering and download link.
* **Manual Edit UI**: Allow users to edit extracted fields before finalizing invoice.
* **Error Dashboard**: Build admin UI for monitoring failed OCR jobs and error reasons.
* **Load Testing**: Perform stress tests on Edge Functions and Supabase limits.

---

## 3. Roadmap

### 3.1 Short‑Term (Next 4 Weeks)

1. **Custom Template Builder**: Let users design and save custom invoice templates.
2. **Multi‑Language UI**: Support Thai/English/Chinese interface elements.
3. **Payment Integration**: Connect Stripe for credit top‑ups.
4. **Audit Logs**: Track user actions for compliance.

### 3.2 Mid‑Term (2–3 Months)

* **Analytics Dashboard**: Usage metrics, OCR accuracy trends, cost reports.
* **Mobile SDK**: iOS/Android libraries for direct camera capture & upload.
* **Batch Processing**: Bulk upload & invoice extraction jobs.
* **Role‑Based Access**: Advanced permissions for teams.

### 3.3 Long‑Term (3–6 Months)

* **Custom AI Training**: Allow organizations to fine‑tune on proprietary invoice formats.
* **Integrations Marketplace**: Pre‑built connectors (SAP, QuickBooks, Zoho).
* **Chatbot Interface**: AI chat assistant for ad‑hoc queries on processed documents.
* **High‑Availability**: Multi‑region deployment & automated failover.

---

## 4. Risks & Mitigations

| Risk                                | Mitigation                                      |
| ----------------------------------- | ----------------------------------------------- |
| Edge Fn cold start latency          | Keep warm via periodic pings                    |
| Low‑confidence OCR segments         | Flag for manual review; allow corrections       |
| Supabase resource limits under load | Use horizontal scaling + caching proxies        |
| Data privacy regulations changes    | Update policies; offer additional data controls |

*Last updated: 2025‑07‑01*
