#!/bin/bash

# Netlify Deployment Script for OCR AI Application
echo "🚀 Starting OCR AI deployment process..."

# Set Node.js version
echo "📦 Setting Node.js version..."
export NODE_VERSION=18

# Install dependencies
echo "📋 Installing dependencies..."
npm ci --legacy-peer-deps

# Run tests (optional - comment out if tests are slow)
echo "🧪 Running tests..."
npm run test -- --passWithNoTests

# Build the application
echo "🏗️  Building application..."
npm run build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📊 Build size analysis..."
    
    # Display build output size
    if [ -d ".next" ]; then
        echo "Build output directory: .next"
        du -sh .next
    fi
    
    echo "🎉 Deployment ready!"
else
    echo "❌ Build failed!"
    exit 1
fi
