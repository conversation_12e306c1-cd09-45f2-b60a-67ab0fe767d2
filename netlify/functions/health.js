exports.handler = async (event, context) => {
  // Health check endpoint for monitoring
  
  const response = {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    },
    body: JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      netlify: {
        deployId: process.env.NETLIFY_DEPLOY_ID,
        commitRef: process.env.COMMIT_REF,
        branchName: process.env.BRANCH,
      },
      services: {
        supabase: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        stripe: !!process.env.STRIPE_SECRET_KEY,
        together: !!process.env.TOGETHER_API_KEY,
      },
    }),
  }

  return response
}
