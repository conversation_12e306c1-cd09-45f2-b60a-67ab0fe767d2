{"name": "ocr-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "netlify:build": "npm run build", "netlify:dev": "netlify dev"}, "dependencies": {"@stripe/stripe-js": "^7.4.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.50.2", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.14", "axios": "^1.6.0", "dotenv": "^17.0.1", "eslint": "^8.42.0", "eslint-config-next": "^13.4.0", "next": "^13.4.0", "openai": "^4.0.0", "postcss": "^8.4.24", "puppeteer": "^19.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "stripe": "^17.7.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}, "devDependencies": {"@netlify/plugin-nextjs": "^4.41.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/jest": "^29.5.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0"}}