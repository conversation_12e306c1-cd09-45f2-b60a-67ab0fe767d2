#!/usr/bin/env node

// Test environment variables
const fs = require('fs');
const path = require('path');

console.log('🔍 Environment Variables Test\n');

// Read .env.local file
const envPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envPath)) {
  console.log('✅ .env.local file exists');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  // Parse environment variables
  const envVars = {};
  envContent.split('\n').forEach(line => {
    if (line.trim() && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  // Check required variables
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
  ];
  
  console.log('\n📋 Required Environment Variables:');
  requiredVars.forEach(varName => {
    const value = envVars[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
    } else {
      console.log(`❌ ${varName}: NOT SET`);
    }
  });
  
  // Test Supabase URL specifically
  const supabaseUrl = envVars['NEXT_PUBLIC_SUPABASE_URL'];
  if (supabaseUrl) {
    console.log('\n🔗 Supabase URL Analysis:');
    console.log(`   URL: ${supabaseUrl}`);
    console.log(`   Valid format: ${supabaseUrl.includes('supabase.co') ? '✅' : '❌'}`);
    console.log(`   Project ID: ${supabaseUrl.split('//')[1]?.split('.')[0] || 'Unknown'}`);
  }
  
} else {
  console.log('❌ .env.local file not found');
}

// Test if we can make a simple request to Supabase
console.log('\n🌐 Testing Supabase Connection...');
const https = require('https');

const testUrl = 'https://gfgygdteyrumaxifgyqg.supabase.co/rest/v1/';
const req = https.get(testUrl, (res) => {
  console.log(`✅ Supabase API accessible (Status: ${res.statusCode})`);
}).on('error', (err) => {
  console.log(`❌ Supabase API error: ${err.message}`);
});

// Test Edge Function
console.log('\n⚡ Testing Edge Functions...');
const functionUrl = 'https://gfgygdteyrumaxifgyqg.supabase.co/functions/v1/stripe-checkout';
const options = {
  method: 'OPTIONS',
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmZ3lnZHRleXJ1bWF4aWZneXFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNDE5MzAsImV4cCI6MjA2NjkxNzkzMH0.emXnfiAIw0KLujLPXoU02kEOSI6E7DWWhrMW1F2JUPQ'
  }
};

const funcReq = https.request(functionUrl, options, (res) => {
  console.log(`Edge Function Response: ${res.statusCode}`);
  if (res.statusCode === 204) {
    console.log('✅ stripe-checkout function is deployed and responding');
  } else if (res.statusCode === 500) {
    console.log('⚠️  stripe-checkout function exists but has runtime errors');
  } else {
    console.log(`❓ Unexpected response: ${res.statusCode}`);
  }
}).on('error', (err) => {
  console.log(`❌ Edge Function error: ${err.message}`);
});

funcReq.end();
