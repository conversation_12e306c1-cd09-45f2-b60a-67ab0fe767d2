/*
  # OCR AI Database Schema

  1. New Tables
    - `user_credits`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users) 
      - `credits_remaining` (integer, default 15)
      - `total_credits_purchased` (integer, default 0)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `uploads`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `filename` (text)
      - `file_type` (text)
      - `file_size` (integer)
      - `status` (text, enum: pending, processing, done, error)
      - `webhook_url` (text)
      - `ai_generated_image_url` (text)
      - `ai_description` (text)
      - `credits_used` (integer, default 1)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `credit_transactions`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `amount` (integer)
      - `transaction_type` (text, enum: purchase, usage, refund)
      - `stripe_payment_intent_id` (text)
      - `upload_id` (uuid, references uploads)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to manage their own data
*/

-- User Credits Table
CREATE TABLE IF NOT EXISTS user_credits (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  credits_remaining integer DEFAULT 15,
  total_credits_purchased integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);

-- Uploads Table  
CREATE TABLE IF NOT EXISTS uploads (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  filename text NOT NULL,
  file_type text NOT NULL,
  file_size integer NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'done', 'error')),
  webhook_url text,
  ai_generated_image_url text,
  ai_description text,
  credits_used integer DEFAULT 1,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Credit Transactions Table
CREATE TABLE IF NOT EXISTS credit_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  amount integer NOT NULL,
  transaction_type text NOT NULL CHECK (transaction_type IN ('purchase', 'usage', 'refund')),
  stripe_payment_intent_id text,
  upload_id uuid REFERENCES uploads,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_credits
CREATE POLICY "Users can read own credits"
  ON user_credits
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own credits"
  ON user_credits
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own credits"
  ON user_credits
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for uploads
CREATE POLICY "Users can read own uploads"
  ON uploads
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own uploads"
  ON uploads
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own uploads"
  ON uploads
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for credit_transactions
CREATE POLICY "Users can read own credit transactions"
  ON credit_transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own credit transactions"
  ON credit_transactions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Function to automatically create user credits on signup
CREATE OR REPLACE FUNCTION handle_new_user() 
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_credits (user_id)
  VALUES (new.id);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY definer;

-- Trigger to create user credits on signup
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE handle_new_user();

-- Function to update user credits updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on user_credits
CREATE TRIGGER update_user_credits_updated_at
    BEFORE UPDATE ON user_credits
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to automatically update updated_at on uploads
CREATE TRIGGER update_uploads_updated_at
    BEFORE UPDATE ON uploads
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to deduct credits and create transaction
CREATE OR REPLACE FUNCTION deduct_credits(
  p_user_id uuid,
  p_upload_id uuid,
  p_credits_to_deduct integer DEFAULT 1
)
RETURNS boolean AS $$
DECLARE
  current_credits integer;
BEGIN
  -- Get current credits with row lock
  SELECT credits_remaining INTO current_credits
  FROM user_credits 
  WHERE user_id = p_user_id
  FOR UPDATE;
  
  -- Check if user has enough credits
  IF current_credits IS NULL OR current_credits < p_credits_to_deduct THEN
    RETURN false;
  END IF;
  
  -- Deduct credits
  UPDATE user_credits 
  SET credits_remaining = credits_remaining - p_credits_to_deduct
  WHERE user_id = p_user_id;
  
  -- Create transaction record
  INSERT INTO credit_transactions (user_id, amount, transaction_type, upload_id)
  VALUES (p_user_id, -p_credits_to_deduct, 'usage', p_upload_id);
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY definer;

-- Function to add credits and create transaction
CREATE OR REPLACE FUNCTION add_credits(
  p_user_id uuid,
  p_credits_to_add integer,
  p_stripe_payment_intent_id text DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  -- Add credits
  INSERT INTO user_credits (user_id, credits_remaining, total_credits_purchased)
  VALUES (p_user_id, p_credits_to_add, p_credits_to_add)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    credits_remaining = user_credits.credits_remaining + p_credits_to_add,
    total_credits_purchased = user_credits.total_credits_purchased + p_credits_to_add;
  
  -- Create transaction record
  INSERT INTO credit_transactions (user_id, amount, transaction_type, stripe_payment_intent_id)
  VALUES (p_user_id, p_credits_to_add, 'purchase', p_stripe_payment_intent_id);
END;
$$ LANGUAGE plpgsql SECURITY definer;
