# Netlify Environment Variables Template
# Copy these to your Netlify site settings > Environment variables

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OCR Service Configuration
TOGETHER_API_KEY=your_together_ai_api_key

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-app-name.netlify.app

# Optional: Analytics and Monitoring
NEXT_PUBLIC_GA_ID=your_google_analytics_id

# Build Configuration
NODE_VERSION=18
NPM_CONFIG_LEGACY_PEER_DEPS=true
