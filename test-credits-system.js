#!/usr/bin/env node

/**
 * OCR AI Credits System Test Script
 * 
 * This script helps verify that your credits system is working correctly.
 * Run after completing the database migration and environment setup.
 */

const https = require('https');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`\n${colors.bold}${colors.blue}=== ${msg} ===${colors.reset}\n`)
};

async function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testEdgeFunction() {
  log.header('Testing Edge Functions');
  
  try {
    const response = await makeRequest({
      hostname: 'gfgygdteyrumaxifgyqg.supabase.co',
      path: '/functions/v1/credits-test',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmZ3lnZHRleXJ1bWF4aWZneXFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNDE5MzAsImV4cCI6MjA2NjkxNzkzMH0.emXnfiAIw0KLujLPXoU02kEOSI6E7DWWhrMW1F2JUPQ'
      }
    }, { name: 'Credits System' });

    if (response.status === 200) {
      log.success('Edge Functions are working correctly');
      log.info(`Response: ${response.data.message}`);
    } else {
      log.error(`Edge Function test failed: ${response.status}`);
    }
  } catch (error) {
    log.error(`Edge Function test error: ${error.message}`);
  }
}

async function testStripeProducts() {
  log.header('Testing Stripe Products');
  
  const products = [
    { id: 'price_1Rg2OM042V10vrfTwKa10siF', name: '10 Credits - $4.99' },
    { id: 'price_1Rg2Oa042V10vrfTpWEQOM2h', name: '50 Credits - $19.99' },
    { id: 'price_1Rg2On042V10vrfTlw6Bb9pz', name: '100 Credits - $34.99' },
    { id: 'price_1Rg2P0042V10vrfTM2yLMNDC', name: '500 Credits - $149.99' }
  ];

  log.success(`${products.length} Stripe credit packages configured:`);
  products.forEach(product => {
    log.info(`  • ${product.name} (${product.id})`);
  });
}

async function checkEnvironmentVariables() {
  log.header('Environment Variables Checklist');
  
  const requiredVars = [
    'SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_URL', 
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
    'STRIPE_WEBHOOK_SECRET'
  ];

  console.log('Please verify these environment variables are set:');
  requiredVars.forEach(varName => {
    console.log(`  [ ] ${varName}`);
  });
  
  console.log('\nIn your .env.local file or deployment environment.');
}

async function provideMigrationSQL() {
  log.header('Database Migration');
  
  console.log('Copy and run this SQL in your Supabase SQL Editor:');
  console.log('https://supabase.com/dashboard/project/gfgygdteyrumaxifgyqg/sql/new\n');
  
  const migrationSQL = `
-- Quick verification query - run this first
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_credits', 'uploads', 'credit_transactions');

-- If the above returns 0 rows, run the full migration from manual_migration_credits_system.sql
`;

  console.log(migrationSQL);
}

async function testLocalDevelopment() {
  log.header('Local Development Test');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/health', // We can add this endpoint
      method: 'GET'
    });

    if (response.status === 200) {
      log.success('Local development server is running');
    } else {
      log.warning('Local development server may not be running');
      log.info('Run: npm run dev');
    }
  } catch (error) {
    log.warning('Local development server is not running');
    log.info('Run: npm run dev');
  }
}

async function main() {
  console.log(`
${colors.bold}${colors.blue}
  ╔════════════════════════════════════════╗
  ║     OCR AI Credits System Test         ║
  ║          Setup Verification            ║
  ╚════════════════════════════════════════╝
${colors.reset}
`);

  await testEdgeFunction();
  await testStripeProducts();
  await checkEnvironmentVariables();
  await provideMigrationSQL();
  await testLocalDevelopment();

  log.header('Next Steps');
  console.log('1. Apply database migration in Supabase dashboard');
  console.log('2. Set up environment variables');
  console.log('3. Configure Stripe webhook');
  console.log('4. Test user registration and credit purchase');
  console.log('5. Test OCR processing with credit deduction');

  log.header('Support');
  console.log('📚 Complete Guide: COMPLETE_SETUP_GUIDE.md');
  console.log('🔧 Migration File: manual_migration_credits_system.sql');
  console.log('📊 Dashboard: https://supabase.com/dashboard/project/gfgygdteyrumaxifgyqg');

  rl.close();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testEdgeFunction, testStripeProducts };
