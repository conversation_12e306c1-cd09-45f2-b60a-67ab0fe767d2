// Core OCR Types
export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface TextLine {
  text: string;
  boundingBox: BoundingBox;
  confidence: number;
  language: 'th' | 'en' | 'zh';
}

export interface OCRResult {
  lines: TextLine[];
  detectedLanguages: string[];
  processingTime: number;
  imageMetadata: {
    width: number;
    height: number;
    format: string;
  };
}

// Invoice Types
export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  lineTotal: number;
}

export interface InvoiceData {
  invoiceNumber: string;
  date: string; // YYYY-MM-DD format
  billingName: string;
  billingAddress: string;
  items: InvoiceItem[];
  subtotal: number;
  taxRate: number;
  total: number;
  currency: string;
}

// Translation Types
export interface TranslationRequest {
  text: string;
  sourceLang: 'th' | 'en' | 'zh';
  targetLang: 'th' | 'en' | 'zh';
  preserveNumbers?: boolean;
  preserveUnits?: boolean;
}

export interface TranslationResult {
  originalText: string;
  translatedText: string;
  sourceLang: string;
  targetLang: string;
  confidence: number;
}

// API Response Types
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

// Rate Limiting Types
export interface RateLimitInfo {
  remaining: number;
  resetTime: number;
  limit: number;
  allowed: boolean;
}

// Processing Status Types
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed';

export interface ProcessingJob {
  id: string;
  status: ProcessingStatus;
  createdAt: string;
  completedAt?: string;
  progress: number;
  result?: OCRResult | InvoiceData | TranslationResult;
  error?: string;
}

// Template Types
export interface TemplateData {
  templateId: string;
  htmlContent: string;
  cssStyles: string;
  placeholders: Record<string, any>;
}

// Configuration Types
export interface AppConfig {
  rateLimit: {
    requestsPerMinute: number;
  };
  ocr: {
    supportedFormats: string[];
    maxFileSize: number;
    languages: string[];
  };
  translation: {
    supportedPairs: Array<{
      source: string;
      target: string;
    }>;
  };
}
