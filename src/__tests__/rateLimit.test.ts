import { getRateLimitHeaders } from '../lib/rateLimit'

// Create a fresh rate limiter for testing
class TestRateLimiter {
  private cache = new Map<string, {requests: number, resetTime: number}>()
  private readonly limit: number = 60
  private readonly windowMs: number = 60000

  check(identifier: string) {
    const now = Date.now()
    const entry = this.cache.get(identifier)

    if (!entry || now > entry.resetTime) {
      this.cache.set(identifier, {
        requests: 1,
        resetTime: now + this.windowMs
      })
      
      return {
        remaining: this.limit - 1,
        resetTime: now + this.windowMs,
        limit: this.limit
      }
    }

    if (entry.requests >= this.limit) {
      return {
        remaining: 0,
        resetTime: entry.resetTime,
        limit: this.limit
      }
    }

    entry.requests++
    
    return {
      remaining: this.limit - entry.requests,
      resetTime: entry.resetTime,
      limit: this.limit
    }
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const entry = this.cache.get(identifier)

    if (!entry || now > entry.resetTime) {
      return true // Fresh window, allowed
    }

    return entry.requests < this.limit
  }
}

describe('Rate Limiter', () => {
  let testRateLimiter: TestRateLimiter

  beforeEach(() => {
    // Create a fresh rate limiter for each test
    testRateLimiter = new TestRateLimiter()
  })

  test('should allow requests within limit', () => {
    const isAllowed = testRateLimiter.isAllowed('test-user-1')
    expect(isAllowed).toBe(true)
  })

  test('should track remaining requests', () => {
    const info = testRateLimiter.check('test-user-2')
    expect(info.remaining).toBe(59) // 60 - 1
    expect(info.limit).toBe(60)
  })

  test('should block requests when limit exceeded', () => {
    // Make 61 requests to exceed the limit (first call in isAllowed counts)
    for (let i = 0; i < 61; i++) {
      testRateLimiter.check('test-user-3')
    }

    // The next check should show remaining as 0 or negative
    const info = testRateLimiter.check('test-user-3')
    expect(info.remaining).toBe(0)
    
    const isAllowed = testRateLimiter.isAllowed('test-user-3')
    expect(isAllowed).toBe(false)
  })

  test('should generate correct headers', () => {
    const rateLimitInfo = {
      remaining: 50,
      resetTime: Date.now() + 60000,
      limit: 60
    }

    const headers = getRateLimitHeaders(rateLimitInfo)
    
    expect(headers['X-RateLimit-Limit']).toBe('60')
    expect(headers['X-RateLimit-Remaining']).toBe('50')
    expect(headers['X-RateLimit-Reset']).toBe(rateLimitInfo.resetTime.toString())
  })
})
