import { processOCRData } from '../utils/ocrUtils'

describe('OCR Utils', () => {
  test('processOCRData should handle valid input', () => {
    const mockRawData = {
      lines: [
        {
          text: 'Sample text',
          boundingBox: { x: 0, y: 0, width: 100, height: 20 },
          confidence: 0.95,
          language: 'en'
        }
      ],
      processingTime: 1000,
      imageMetadata: {
        width: 800,
        height: 600,
        format: 'jpeg'
      }
    }

    const result = processOCRData(mockRawData)

    expect(result.lines).toHaveLength(1)
    expect(result.lines[0].text).toBe('Sample text')
    expect(result.detectedLanguages).toContain('en')
    expect(result.processingTime).toBe(1000)
  })

  test('processOCRData should handle empty input', () => {
    const result = processOCRData({})

    expect(result.lines).toHaveLength(0)
    expect(result.detectedLanguages).toHaveLength(0)
    expect(result.processingTime).toBe(0)
  })

  test('processOCRData should handle null input', () => {
    const result = processOCRData(null)

    expect(result.lines).toHaveLength(0)
    expect(result.detectedLanguages).toHaveLength(0)
    expect(result.processingTime).toBe(0)
  })
})
