import { OCRResult, TextLine } from '../types';

// Sample OCR processing utility
export function processOCRData(rawData: any): OCRResult {
  const lines: TextLine[] = rawData?.lines?.map((line: any) => ({
    text: line.text,
    boundingBox: line.boundingBox,
    confidence: line.confidence,
    language: line.language,
  })) ?? [];

  return {
    lines,
    detectedLanguages: Array.from(new Set(lines.map(line => line.language))),
    processingTime: rawData?.processingTime ?? 0,
    imageMetadata: {
      width: rawData?.imageMetadata?.width ?? 0,
      height: rawData?.imageMetadata?.height ?? 0,
      format: rawData?.imageMetadata?.format ?? 'unknown',
    },
  };
}
