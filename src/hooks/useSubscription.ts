'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/auth'
import { getProductByPriceId } from '@/stripe-config'

export interface UserSubscription {
  subscription_status: string
  price_id: string | null
  current_period_start: number | null
  current_period_end: number | null
  cancel_at_period_end: boolean
  payment_method_brand: string | null
  payment_method_last4: string | null
  product_name?: string
}

export function useSubscription() {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchSubscription() {
      try {
        const { data, error } = await supabase
          .from('stripe_user_subscriptions')
          .select('*')
          .maybeSingle()

        if (error) {
          setError(error.message)
          return
        }

        if (data) {
          const product = data.price_id ? getProductByPriceId(data.price_id) : null
          setSubscription({
            ...data,
            product_name: product?.name
          })
        } else {
          setSubscription(null)
        }
      } catch (err) {
        setError('Failed to fetch subscription')
      } finally {
        setLoading(false)
      }
    }

    fetchSubscription()
  }, [])

  return { subscription, loading, error, refetch: () => setLoading(true) }
}