'use client'

import { useState } from 'react'
import { StorageService, UploadResult } from '@/lib/storage'
import { useAuth } from './useAuth'

export function useStorage() {
  const { user } = useAuth()
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  const uploadFile = async (
    file: File,
    options?: {
      folder?: string
      generatePath?: boolean
      onProgress?: (progress: number) => void
    }
  ): Promise<UploadResult> => {
    if (!user) {
      return { path: '', url: '', error: 'User not authenticated' }
    }

    setUploading(true)
    setUploadProgress(0)

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const next = prev + 10
          if (next >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          options?.onProgress?.(next)
          return next
        })
      }, 100)

      const storageService = new StorageService()
      const result = await storageService.uploadFile(file, user.id, options)

      clearInterval(progressInterval)
      setUploadProgress(100)
      options?.onProgress?.(100)

      return result
    } catch (error) {
      return { 
        path: '', 
        url: '', 
        error: error instanceof Error ? error.message : 'Upload failed' 
      }
    } finally {
      setUploading(false)
      setTimeout(() => setUploadProgress(0), 1000)
    }
  }

  const deleteFile = async (bucket: string, path: string) => {
    return StorageService.deleteFile(bucket, path)
  }

  const getSignedUrl = async (bucket: string, path: string, expiresIn?: number) => {
    return StorageService.getSignedUrl(bucket, path, expiresIn)
  }

  const listUserFiles = async (bucket: string, folder?: string) => {
    if (!user) {
      return { files: [], error: 'User not authenticated' }
    }
    return StorageService.listUserFiles(bucket, user.id, folder)
  }

  const saveProcessedResult = async (
    originalFileName: string,
    result: any,
    type: 'ocr' | 'invoice' | 'translation' = 'ocr'
  ) => {
    if (!user) {
      return { path: '', url: '', error: 'User not authenticated' }
    }
    return StorageService.saveProcessedResult(user.id, originalFileName, result, type)
  }

  return {
    uploadFile,
    deleteFile,
    getSignedUrl,
    listUserFiles,
    saveProcessedResult,
    uploading,
    uploadProgress
  }
}