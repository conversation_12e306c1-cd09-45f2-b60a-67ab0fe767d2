'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { creditsService, UserCredits } from '@/lib/credits'

export function useCredits() {
  const { user } = useAuth()
  const [credits, setCredits] = useState<UserCredits | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchCredits() {
      if (!user) {
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const userCredits = await creditsService.getUserCredits(user.id)
        setCredits(userCredits)
        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch credits')
      } finally {
        setLoading(false)
      }
    }

    fetchCredits()
  }, [user])

  const refreshCredits = async () => {
    if (!user) return
    
    try {
      const userCredits = await creditsService.getUserCredits(user.id)
      setCredits(userCredits)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh credits')
    }
  }

  return {
    credits,
    loading,
    error,
    refreshCredits,
    hasEnoughCredits: (required: number = 1) => credits ? credits.credits_remaining >= required : false
  }
}
