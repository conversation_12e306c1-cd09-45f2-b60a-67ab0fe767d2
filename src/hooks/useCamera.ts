'use client'

import { useState, useRef, useCallback } from 'react'

export interface CameraCapabilities {
  hasCamera: boolean
  hasMultipleCameras: boolean
  supportsCameraAPI: boolean
  isMobile: boolean
}

export function useCamera() {
  const [isCapturing, setIsCapturing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [capabilities, setCapabilities] = useState<CameraCapabilities>({
    hasCamera: false,
    hasMultipleCameras: false,
    supportsCameraAPI: false,
    isMobile: false
  })
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  // Check camera capabilities
  const checkCapabilities = useCallback(async (): Promise<CameraCapabilities> => {
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    const supportsCameraAPI = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    
    let hasCamera = false
    let hasMultipleCameras = false

    if (supportsCameraAPI) {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices()
        const videoDevices = devices.filter(device => device.kind === 'videoinput')
        hasCamera = videoDevices.length > 0
        hasMultipleCameras = videoDevices.length > 1
      } catch (err) {
        console.warn('Could not enumerate devices:', err)
      }
    }

    const caps = {
      hasCamera,
      hasMultipleCameras,
      supportsCameraAPI,
      isMobile
    }
    
    setCapabilities(caps)
    return caps
  }, [])

  // Start camera stream
  const startCamera = useCallback(async (facingMode: 'user' | 'environment' = 'environment') => {
    setError(null)
    setIsCapturing(true)

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera API not supported')
      }

      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: { ideal: facingMode },
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        },
        audio: false
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      streamRef.current = stream

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()
      }

      return stream
    } catch (err: any) {
      const errorMessage = err.name === 'NotAllowedError' 
        ? 'Camera access denied. Please allow camera permissions.'
        : err.name === 'NotFoundError'
        ? 'No camera found on this device.'
        : err.name === 'NotSupportedError'
        ? 'Camera not supported on this device.'
        : 'Failed to access camera: ' + err.message

      setError(errorMessage)
      setIsCapturing(false)
      throw new Error(errorMessage)
    }
  }, [])

  // Stop camera stream
  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
    
    setIsCapturing(false)
  }, [])

  // Capture photo from video stream
  const capturePhoto = useCallback((): Promise<File | null> => {
    return new Promise((resolve) => {
      if (!videoRef.current || !canvasRef.current) {
        resolve(null)
        return
      }

      const video = videoRef.current
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      if (!context) {
        resolve(null)
        return
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Convert canvas to blob
      canvas.toBlob((blob) => {
        if (blob) {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
          const file = new File([blob], `camera-capture-${timestamp}.jpg`, {
            type: 'image/jpeg',
            lastModified: Date.now()
          })
          resolve(file)
        } else {
          resolve(null)
        }
      }, 'image/jpeg', 0.9)
    })
  }, [])

  // Switch camera (front/back)
  const switchCamera = useCallback(async (facingMode: 'user' | 'environment') => {
    if (isCapturing) {
      stopCamera()
      await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
      await startCamera(facingMode)
    }
  }, [isCapturing, stopCamera, startCamera])

  return {
    // State
    isCapturing,
    error,
    capabilities,
    
    // Refs for components
    videoRef,
    canvasRef,
    
    // Methods
    checkCapabilities,
    startCamera,
    stopCamera,
    capturePhoto,
    switchCamera,
    
    // Utilities
    clearError: () => setError(null)
  }
}
