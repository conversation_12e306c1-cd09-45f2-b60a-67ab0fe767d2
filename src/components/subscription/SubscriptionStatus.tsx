'use client'

import { useSubscription } from '@/hooks/useSubscription'
import { Alert } from '@/components/ui/Alert'

export function SubscriptionStatus() {
  const { subscription, loading, error } = useSubscription()

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert type="error" message={error} />
    )
  }

  if (!subscription || subscription.subscription_status === 'not_started') {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-yellow-800 mb-2">No Active Subscription</h3>
        <p className="text-yellow-700">You don't have an active subscription. Subscribe to access premium features.</p>
      </div>
    )
  }

  const isActive = ['active', 'trialing'].includes(subscription.subscription_status)
  const statusColor = isActive ? 'green' : subscription.subscription_status === 'past_due' ? 'yellow' : 'red'

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Subscription Status</h3>
        <span className={`px-3 py-1 rounded-full text-sm font-medium bg-${statusColor}-100 text-${statusColor}-800`}>
          {subscription.subscription_status.replace('_', ' ').toUpperCase()}
        </span>
      </div>

      {subscription.product_name && (
        <p className="text-gray-600 mb-2">
          <span className="font-medium">Plan:</span> {subscription.product_name}
        </p>
      )}

      {subscription.current_period_end && (
        <p className="text-gray-600 mb-2">
          <span className="font-medium">
            {subscription.cancel_at_period_end ? 'Expires:' : 'Renews:'}
          </span>{' '}
          {new Date(subscription.current_period_end * 1000).toLocaleDateString()}
        </p>
      )}

      {subscription.payment_method_brand && subscription.payment_method_last4 && (
        <p className="text-gray-600">
          <span className="font-medium">Payment Method:</span>{' '}
          {subscription.payment_method_brand.toUpperCase()} ending in {subscription.payment_method_last4}
        </p>
      )}

      {subscription.cancel_at_period_end && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-yellow-800 text-sm">
            Your subscription will not renew and will expire at the end of the current period.
          </p>
        </div>
      )}
    </div>
  )
}