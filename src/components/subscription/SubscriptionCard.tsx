'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Alert } from '@/components/ui/Alert'
import { StripeProduct } from '@/stripe-config'
import { supabase } from '@/lib/auth'

interface SubscriptionCardProps {
  product: StripeProduct
  isCurrentPlan?: boolean
}

export function SubscriptionCard({ product, isCurrentPlan = false }: SubscriptionCardProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubscribe = async () => {
    setLoading(true)
    setError(null)

    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        setError('Please sign in to subscribe')
        setLoading(false)
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          price_id: product.priceId,
          mode: product.mode,
          success_url: `${window.location.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${window.location.origin}/pricing`,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session')
      }

      if (data.url) {
        window.location.href = data.url
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setLoading(false)
    }
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 border-2 ${isCurrentPlan ? 'border-green-500' : 'border-gray-200'}`}>
      {isCurrentPlan && (
        <div className="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full inline-block mb-4">
          Current Plan
        </div>
      )}
      
      <h3 className="text-xl font-bold text-gray-900 mb-2">{product.name}</h3>
      <p className="text-gray-600 mb-4">{product.description}</p>
      
      {product.price && (
        <div className="mb-6">
          <span className="text-3xl font-bold text-gray-900">${product.price}</span>
          {product.mode === 'subscription' && (
            <span className="text-gray-600 ml-1">/month</span>
          )}
        </div>
      )}

      {error && (
        <Alert 
          type="error" 
          message={error} 
          onClose={() => setError(null)}
        />
      )}

      <Button
        onClick={handleSubscribe}
        loading={loading}
        disabled={isCurrentPlan}
        className="w-full"
      >
        {isCurrentPlan ? 'Current Plan' : product.mode === 'subscription' ? 'Subscribe' : 'Purchase'}
      </Button>
    </div>
  )
}