'use client'

import { useState } from 'react'
import { useCredits } from '@/hooks/useCredits'
import { useStripe } from '@/lib/stripe-service'
import { useAuth } from '@/hooks/useAuth'
import { creditPackages } from '@/stripe-config'
import { Button } from '@/components/ui/Button'

export function CreditsOverview() {
  const { user } = useAuth()
  const { credits, loading, error, refreshCredits } = useCredits()
  const { purchaseCredits, loading: purchaseLoading, error: purchaseError } = useStripe()
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null)

  const handlePurchase = async (packageId: string) => {
    if (!user) return
    
    try {
      setSelectedPackage(packageId)
      await purchaseCredits(packageId, user.id)
    } catch (error) {
      console.error('Purchase failed:', error)
    } finally {
      setSelectedPackage(null)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-16 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-red-600">
          <h3 className="text-lg font-semibold mb-2">Error Loading Credits</h3>
          <p>{error}</p>
          <Button onClick={refreshCredits} className="mt-2">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Current Credits Display */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Your Credits</h2>
            <div className="flex items-baseline space-x-2">
              <span className="text-4xl font-bold">
                {credits?.credits_remaining ?? 0}
              </span>
              <span className="text-blue-100">credits remaining</span>
            </div>
            {credits && (
              <p className="text-blue-100 mt-2">
                Total purchased: {credits.total_credits_purchased} credits
              </p>
            )}
          </div>
          <div className="text-right">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Credit Packages */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-xl font-semibold mb-6">Buy More Credits</h3>
        
        {purchaseError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-600">{purchaseError}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {creditPackages.map((pkg) => (
            <div
              key={pkg.id}
              className={`relative border rounded-lg p-6 hover:shadow-lg transition-shadow ${
                pkg.popular ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
            >
              {pkg.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              {pkg.savings && (
                <div className="absolute -top-2 -right-2">
                  <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                    {pkg.savings}
                  </span>
                </div>
              )}

              <div className="text-center">
                <h4 className="text-lg font-semibold mb-2">{pkg.name}</h4>
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  ${pkg.price}
                </div>
                <div className="text-gray-600 mb-4">
                  {pkg.credits} credits
                </div>
                <p className="text-sm text-gray-500 mb-6">
                  {pkg.description}
                </p>
                <Button
                  onClick={() => handlePurchase(pkg.id)}
                  disabled={purchaseLoading && selectedPackage === pkg.id}
                  className={`w-full ${
                    pkg.popular 
                      ? 'bg-blue-500 hover:bg-blue-600' 
                      : 'bg-gray-800 hover:bg-gray-900'
                  }`}
                >
                  {purchaseLoading && selectedPackage === pkg.id ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>Processing...</span>
                    </div>
                  ) : (
                    `Buy ${pkg.credits} Credits`
                  )}
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-2">💡 How Credits Work</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Each OCR operation uses 1 credit</li>
            <li>• Credits never expire</li>
            <li>• New users get 15 free credits to get started</li>
            <li>• Volume discounts available for larger packages</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
