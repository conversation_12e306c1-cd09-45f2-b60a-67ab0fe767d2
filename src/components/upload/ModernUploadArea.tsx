'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { Button } from '@/components/ui/Button'
import { Alert } from '@/components/ui/Alert'
import { CameraCapture } from './CameraCapture'

interface ModernUploadAreaProps {
  onFileSelect: (file: File) => void
  onUploadStart: () => void
  onUploadComplete: (result: any) => void
  onUploadError: (error: string) => void
  loading?: boolean
  selectedLanguage?: string
}

export function ModernUploadArea({
  onFileSelect,
  onUploadStart,
  onUploadComplete,
  onUploadError,
  loading = false,
  selectedLanguage = 'auto'
}: ModernUploadAreaProps) {
  const { user } = useAuth()
  const { credits } = useCredits()
  const [dragOver, setDragOver] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadMethod, setUploadMethod] = useState<'file' | 'camera' | 'url'>('file')
  const [urlInput, setUrlInput] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [showCameraCapture, setShowCameraCapture] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const cameraInputRef = useRef<HTMLInputElement>(null)

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const creditsRemaining = credits?.credits_remaining ?? 0
  const hasEnoughCredits = creditsRemaining >= 1

  const validateFile = (file: File): string | null => {
    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg']
    
    if (file.size > maxSize) {
      return 'File too large. Maximum size is 10MB'
    }
    
    if (!allowedTypes.includes(file.type)) {
      return 'Invalid file type. Only JPEG, PNG, and WebP are supported'
    }
    
    return null
  }

  const handleFileSelection = useCallback((file: File) => {
    setError(null)
    
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      onUploadError(validationError)
      return
    }

    setSelectedFile(file)
    onFileSelect(file)
  }, [onFileSelect, onUploadError])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFileSelection(files[0])
    }
  }, [handleFileSelection])

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelection(files[0])
    }
  }

  const handleCameraCapture = () => {
    if (isMobile && navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function') {
      setShowCameraCapture(true)
    } else if (cameraInputRef.current) {
      cameraInputRef.current.click()
    }
  }

  const handleCameraCaptureComplete = (file: File) => {
    setShowCameraCapture(false)
    handleFileSelection(file)
  }

  const handleUrlUpload = async () => {
    if (!urlInput.trim()) {
      setError('Please enter a valid image URL')
      return
    }

    try {
      setError(null)
      const response = await fetch(urlInput)
      const blob = await response.blob()
      
      if (!blob.type.startsWith('image/')) {
        setError('URL does not point to a valid image')
        return
      }

      const file = new File([blob], 'url-image.jpg', { type: blob.type })
      handleFileSelection(file)
    } catch (error) {
      setError('Failed to load image from URL')
    }
  }

  const handleUpload = async () => {
    if (!selectedFile || !hasEnoughCredits) return

    onUploadStart()
    
    try {
      const formData = new FormData()
      formData.append('image', selectedFile)
      formData.append('language', selectedLanguage)

      const response = await fetch('/api/ocr', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        onUploadComplete(data.data)
        setSelectedFile(null)
        setUrlInput('')
        // Reset file inputs
        if (fileInputRef.current) fileInputRef.current.value = ''
        if (cameraInputRef.current) cameraInputRef.current.value = ''
      } else {
        onUploadError(data.error || 'OCR processing failed')
      }
    } catch (error) {
      onUploadError('Upload failed. Please try again.')
    }
  }

  if (!user) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
        <div className="text-gray-500 mb-4">
          <svg className="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <p className="text-lg font-medium">Sign in required</p>
          <p className="text-sm">Please sign in to use OCR features</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Upload Method Selector */}
      <div className="flex justify-center">
        <div className={`flex bg-gray-100 rounded-lg p-1 ${isMobile ? 'w-full max-w-sm' : ''}`}>
          <button
            onClick={() => setUploadMethod('file')}
            className={`${isMobile ? 'flex-1' : 'px-4'} py-2 rounded-md text-sm font-medium transition-colors ${
              uploadMethod === 'file'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <span className={isMobile ? 'block' : 'inline'}>📁</span>
            <span className={isMobile ? 'block text-xs' : 'ml-1'}>File</span>
          </button>
          <button
            onClick={() => setUploadMethod('camera')}
            className={`${isMobile ? 'flex-1' : 'px-4'} py-2 rounded-md text-sm font-medium transition-colors ${
              uploadMethod === 'camera'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <span className={isMobile ? 'block' : 'inline'}>📷</span>
            <span className={isMobile ? 'block text-xs' : 'ml-1'}>Camera</span>
          </button>
          <button
            onClick={() => setUploadMethod('url')}
            className={`${isMobile ? 'flex-1' : 'px-4'} py-2 rounded-md text-sm font-medium transition-colors ${
              uploadMethod === 'url'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <span className={isMobile ? 'block' : 'inline'}>🔗</span>
            <span className={isMobile ? 'block text-xs' : 'ml-1'}>URL</span>
          </button>
        </div>
      </div>

      {/* Upload Area */}
      <div className="relative">
        {uploadMethod === 'file' && (
          <div
            className={`
              border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer
              ${dragOver ? 'border-blue-400 bg-blue-50 scale-105' : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'}
              ${loading ? 'pointer-events-none opacity-50' : ''}
            `}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              className="hidden"
            />
            
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              
              <div>
                <p className="text-lg font-medium text-gray-900">
                  {selectedFile ? selectedFile.name : 'Drop your image here, or click to browse'}
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Supports JPEG, PNG, WebP • Max 10MB
                </p>
              </div>
            </div>
          </div>
        )}

        {uploadMethod === 'camera' && (
          <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center">
            <input
              ref={cameraInputRef}
              type="file"
              accept="image/*"
              capture="environment"
              onChange={handleFileInputChange}
              className="hidden"
            />
            
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              
              <div>
                <p className="text-lg font-medium text-gray-900">
                  {selectedFile ? selectedFile.name : 'Take a photo with your camera'}
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Perfect for documents, receipts, and text images
                </p>
              </div>
              
              <Button
                onClick={handleCameraCapture}
                variant="outline"
                className={`mt-4 ${isMobile ? 'w-full py-3 text-lg' : ''}`}
              >
                📷 {isMobile ? 'Take Photo' : 'Open Camera'}
              </Button>
            </div>
          </div>
        )}

        {uploadMethod === 'url' && (
          <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center">
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              
              <div>
                <p className="text-lg font-medium text-gray-900">
                  Upload image from URL
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Enter a direct link to an image file
                </p>
              </div>
              
              <div className="max-w-md mx-auto">
                <div className="flex space-x-2">
                  <input
                    type="url"
                    value={urlInput}
                    onChange={(e) => setUrlInput(e.target.value)}
                    placeholder="https://example.com/image.jpg"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <Button onClick={handleUrlUpload} variant="outline">
                    Load
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert type="error" message={error} onClose={() => setError(null)} />
      )}

      {/* Credits Warning */}
      {!hasEnoughCredits && (
        <Alert 
          type="warning" 
          message={`Insufficient credits. You need 1 credit to process an image. Current balance: ${creditsRemaining}`}
        />
      )}

      {/* Upload Button */}
      {selectedFile && (
        <div className="text-center">
          <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4 inline-block">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="text-left">
                <p className="font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB • Language: {selectedLanguage}
                </p>
              </div>
            </div>
          </div>
          
          <Button
            onClick={handleUpload}
            disabled={loading || !hasEnoughCredits}
            loading={loading}
            size="lg"
            className="px-8"
          >
            {loading ? 'Processing...' : `Extract Text (1 Credit)`}
          </Button>
        </div>
      )}

      {/* Camera Capture Modal */}
      {showCameraCapture && (
        <CameraCapture
          onCapture={handleCameraCaptureComplete}
          onClose={() => setShowCameraCapture(false)}
        />
      )}
    </div>
  )
}
