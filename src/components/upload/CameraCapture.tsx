'use client'

import { useState, useEffect } from 'react'
import { useCamera } from '@/hooks/useCamera'
import { Button } from '@/components/ui/Button'
import { Alert } from '@/components/ui/Alert'

interface CameraCaptureProps {
  onCapture: (file: File) => void
  onClose: () => void
  className?: string
}

export function CameraCapture({ onCapture, onClose, className = '' }: CameraCaptureProps) {
  const {
    isCapturing,
    error,
    capabilities,
    videoRef,
    canvasRef,
    checkCapabilities,
    startCamera,
    stopCamera,
    capturePhoto,
    switchCamera,
    clearError
  } = useCamera()

  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment')
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const init = async () => {
      await checkCapabilities()
      setIsInitialized(true)
    }
    init()
  }, [checkCapabilities])

  useEffect(() => {
    if (isInitialized && capabilities.supportsCameraAPI) {
      startCamera(facingMode)
    }

    return () => {
      stopCamera()
    }
  }, [isInitialized, capabilities.supportsCameraAPI, facingMode, startCamera, stopCamera])

  const handleCapture = async () => {
    try {
      const file = await capturePhoto()
      if (file) {
        onCapture(file)
        onClose()
      }
    } catch (err) {
      console.error('Capture failed:', err)
    }
  }

  const handleSwitchCamera = () => {
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user'
    setFacingMode(newFacingMode)
    switchCamera(newFacingMode)
  }

  if (!isInitialized) {
    return (
      <div className={`fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 ${className}`}>
        <div className="bg-white rounded-lg p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Initializing camera...</p>
        </div>
      </div>
    )
  }

  if (!capabilities.supportsCameraAPI) {
    return (
      <div className={`fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 ${className}`}>
        <div className="bg-white rounded-lg p-6 max-w-sm w-full text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Camera Not Available</h3>
          <p className="text-gray-600 mb-4">
            Your device or browser doesn't support camera access.
          </p>
          <Button onClick={onClose} variant="outline" className="w-full">
            Close
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={`fixed inset-0 bg-black z-50 ${className}`}>
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-black bg-opacity-50 p-4">
        <div className="flex justify-between items-center">
          <button
            onClick={onClose}
            className="text-white p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-75 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          
          <h2 className="text-white font-medium">Take Photo</h2>
          
          {capabilities.hasMultipleCameras && (
            <button
              onClick={handleSwitchCamera}
              className="text-white p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-75 transition-colors"
              disabled={!isCapturing}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Camera View */}
      <div className="relative w-full h-full flex items-center justify-center">
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full h-full object-cover"
          style={{ transform: facingMode === 'user' ? 'scaleX(-1)' : 'none' }}
        />
        
        {/* Overlay Grid */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="w-full h-full border-2 border-white border-opacity-30">
            <div className="w-full h-1/3 border-b border-white border-opacity-30"></div>
            <div className="w-full h-1/3 border-b border-white border-opacity-30 flex">
              <div className="w-1/3 h-full border-r border-white border-opacity-30"></div>
              <div className="w-1/3 h-full border-r border-white border-opacity-30"></div>
              <div className="w-1/3 h-full"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="absolute bottom-0 left-0 right-0 p-6 bg-black bg-opacity-50">
        <div className="flex justify-center items-center space-x-8">
          {/* Cancel Button */}
          <button
            onClick={onClose}
            className="text-white text-sm font-medium px-4 py-2 rounded-lg bg-gray-600 hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>

          {/* Capture Button */}
          <button
            onClick={handleCapture}
            disabled={!isCapturing}
            className="w-16 h-16 bg-white rounded-full border-4 border-gray-300 hover:border-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-400"></div>
          </button>

          {/* Info */}
          <div className="text-white text-sm text-center">
            <div className="font-medium">Tap to capture</div>
            <div className="text-xs text-gray-300">
              {facingMode === 'environment' ? 'Back camera' : 'Front camera'}
            </div>
          </div>
        </div>
      </div>

      {/* Hidden canvas for capture */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Error Display */}
      {error && (
        <div className="absolute top-20 left-4 right-4 z-20">
          <div className="bg-red-600 text-white border border-red-700 rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{error}</p>
                </div>
              </div>
              <button
                onClick={clearError}
                className="flex-shrink-0 ml-4 text-red-300 hover:text-white"
              >
                <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {!isCapturing && !error && (
        <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p>Starting camera...</p>
          </div>
        </div>
      )}
    </div>
  )
}
