'use client'

import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { useSubscription } from '@/hooks/useSubscription'
import { Button } from '@/components/ui/Button'
import { CreditsIndicator } from '@/components/ui/CreditsIndicator'
import { LanguageSelector } from '@/components/ui/LanguageSelector'
import { signOut } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'

export function Navigation() {
  const { user, loading } = useAuth()
  const { subscription } = useSubscription()
  const router = useRouter()
  const [selectedLanguage, setSelectedLanguage] = useState('auto')

  // Load saved language preference
  useEffect(() => {
    const savedLanguage = localStorage.getItem('ocr-language') || 'auto'
    setSelectedLanguage(savedLanguage)
  }, [])

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language)
    localStorage.setItem('ocr-language', language)
    // Dispatch custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: language }))
  }

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-2">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-blue rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <h1 className="text-xl font-bold text-text-dark">OCR AI</h1>
            </Link>
            
            {user && subscription && subscription.subscription_status === 'active' && (
              <span className="ml-4 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                {subscription.product_name || 'Pro'}
              </span>
            )}
          </div>
          
          <nav className="flex items-center space-x-6">
            {user && (
              <Link href="/upload" className="text-text-light hover:text-text-dark transition-colors font-medium">
                Upload
              </Link>
            )}
            <Link href="/pricing" className="text-text-light hover:text-text-dark transition-colors">
              Pricing
            </Link>
            <a href="#" className="text-text-light hover:text-text-dark transition-colors">
              Docs
            </a>
            <a href="#" className="text-text-light hover:text-text-dark transition-colors">
              API
            </a>
            
            {loading ? (
              <div className="w-8 h-8 animate-pulse bg-gray-200 rounded"></div>
            ) : user ? (
              <div className="flex items-center space-x-3">
                <LanguageSelector
                  selectedLanguage={selectedLanguage}
                  onLanguageChange={handleLanguageChange}
                />
                <CreditsIndicator />
                <Link href="/account" className="text-text-light hover:text-text-dark transition-colors">
                  Account
                </Link>
                <Button variant="ghost" size="sm" onClick={handleSignOut}>
                  Sign Out
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link href="/login">
                  <Button variant="ghost" size="sm">
                    Sign In
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button size="sm">
                    Sign Up
                  </Button>
                </Link>
              </div>
            )}
          </nav>
        </div>
      </div>
    </header>
  )
}