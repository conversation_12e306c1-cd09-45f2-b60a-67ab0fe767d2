'use client'

interface FileInfoPanelProps {
  file: File
  onRemove: () => void
  onReplace: () => void
  selectedLanguage?: string
}

export function FileInfoPanel({ file, onRemove, onReplace, selectedLanguage = 'auto' }: FileInfoPanelProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileTypeDisplay = (type: string): string => {
    const typeMap: { [key: string]: string } = {
      'image/jpeg': 'JPEG Image',
      'image/jpg': 'JPG Image', 
      'image/png': 'PNG Image',
      'image/webp': 'WebP Image'
    }
    return typeMap[type] || type
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-start space-x-4">
        {/* File Icon */}
        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>

        {/* File Details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 truncate" title={file.name}>
                {file.name}
              </h4>
              <div className="mt-1 space-y-1">
                <p className="text-xs text-gray-500">
                  <span className="font-medium">Size:</span> {formatFileSize(file.size)}
                </p>
                <p className="text-xs text-gray-500">
                  <span className="font-medium">Type:</span> {getFileTypeDisplay(file.type)}
                </p>
                <p className="text-xs text-gray-500">
                  <span className="font-medium">Language:</span> {selectedLanguage === 'auto' ? 'Auto-detect' : selectedLanguage.toUpperCase()}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2 ml-4 flex-shrink-0">
              <button
                onClick={onReplace}
                className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md transition-colors"
                title="Choose a different file"
              >
                Replace
              </button>
              <button
                onClick={onRemove}
                className="text-xs bg-red-100 hover:bg-red-200 text-red-700 px-2 py-1 rounded-md transition-colors"
                title="Remove this file"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
