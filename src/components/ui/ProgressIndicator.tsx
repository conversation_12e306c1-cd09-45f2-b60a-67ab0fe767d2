'use client'

interface ProgressIndicatorProps {
  progress: number // 0-100
  status: string
  isVisible: boolean
}

export function ProgressIndicator({ progress, status, isVisible }: ProgressIndicatorProps) {
  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <div className="text-center space-y-6">
          {/* Spinner */}
          <div className="mx-auto w-16 h-16 relative">
            <div className="absolute inset-0 border-4 border-blue-100 rounded-full"></div>
            <div 
              className="absolute inset-0 border-4 border-blue-600 rounded-full border-t-transparent animate-spin"
              style={{
                animationDuration: '1s'
              }}
            ></div>
          </div>

          {/* Progress Text */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Processing Your Upload
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {status}
            </p>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            ></div>
          </div>

          {/* Progress Percentage */}
          <div className="text-sm font-medium text-gray-700">
            {Math.round(progress)}% Complete
          </div>
        </div>
      </div>
    </div>
  )
}
