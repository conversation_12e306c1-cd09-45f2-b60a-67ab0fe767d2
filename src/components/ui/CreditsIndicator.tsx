'use client'

import { useCredits } from '@/hooks/useCredits'
import { useAuth } from '@/hooks/useAuth'
import { useState, useEffect } from 'react'
import { clientCreditsService } from '@/lib/credits-client'
import Link from 'next/link'

export function CreditsIndicator() {
  const { user } = useAuth()
  const { credits, loading, refreshCredits } = useCredits()
  const [stats, setStats] = useState({ totalUsed: 0, totalPurchased: 0 })
  const [showTooltip, setShowTooltip] = useState(false)

  useEffect(() => {
    if (user) {
      clientCreditsService.getTransactionStats(user.id).then(setStats)
    }
  }, [user, credits])

  if (!user || loading) {
    return null
  }

  const creditsRemaining = credits?.credits_remaining ?? 0
  const totalCredits = credits?.total_credits_purchased ?? 0
  const isLowCredits = creditsRemaining <= 5
  const isCriticalCredits = creditsRemaining <= 2

  return (
    <div className="relative">
      <Link
        href="/dashboard/credits"
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
          isCriticalCredits
            ? 'bg-red-50 hover:bg-red-100 text-red-700 border border-red-200'
            : isLowCredits
            ? 'bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border border-yellow-200'
            : 'bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200'
        }`}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        <div className="flex items-center space-x-1">
          <svg
            className="w-4 h-4"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
              clipRule="evenodd"
            />
          </svg>
          <span className="font-semibold text-sm">
            {creditsRemaining}
          </span>
        </div>
        <span className="text-xs font-medium hidden sm:inline">
          credit{creditsRemaining !== 1 ? 's' : ''}
        </span>
        {isCriticalCredits && (
          <span className="text-xs bg-red-200 text-red-800 px-1.5 py-0.5 rounded-full font-medium">
            Critical
          </span>
        )}
        {isLowCredits && !isCriticalCredits && (
          <span className="text-xs bg-yellow-200 text-yellow-800 px-1.5 py-0.5 rounded-full font-medium">
            Low
          </span>
        )}
      </Link>

      {/* Tooltip */}
      {showTooltip && (
        <div className="absolute top-full left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-3">
          <div className="text-sm">
            <div className="font-medium text-gray-900 mb-2">Credit Usage</div>
            <div className="space-y-1 text-gray-600">
              <div className="flex justify-between">
                <span>Remaining:</span>
                <span className="font-medium">{creditsRemaining}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Purchased:</span>
                <span className="font-medium">{totalCredits}</span>
              </div>
              <div className="flex justify-between">
                <span>Used:</span>
                <span className="font-medium">{stats.totalUsed}</span>
              </div>
            </div>
            <div className="mt-2 pt-2 border-t border-gray-100">
              <div className="text-xs text-blue-600 font-medium">
                Click to manage credits →
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
