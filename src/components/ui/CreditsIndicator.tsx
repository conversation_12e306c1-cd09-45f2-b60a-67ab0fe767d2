'use client'

import { useCredits } from '@/hooks/useCredits'
import { useAuth } from '@/hooks/useAuth'
import Link from 'next/link'

export function CreditsIndicator() {
  const { user } = useAuth()
  const { credits, loading } = useCredits()

  if (!user || loading) {
    return null
  }

  const creditsRemaining = credits?.credits_remaining ?? 0
  const isLowCredits = creditsRemaining <= 5

  return (
    <Link 
      href="/dashboard/credits"
      className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
        isLowCredits 
          ? 'bg-red-50 hover:bg-red-100 text-red-700' 
          : 'bg-blue-50 hover:bg-blue-100 text-blue-700'
      }`}
    >
      <div className="flex items-center space-x-1">
        <svg 
          className="w-4 h-4" 
          fill="currentColor" 
          viewBox="0 0 20 20"
        >
          <path 
            fillRule="evenodd" 
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
            clipRule="evenodd" 
          />
        </svg>
        <span className="font-medium text-sm">
          {creditsRemaining}
        </span>
      </div>
      <span className="text-xs">
        credit{creditsRemaining !== 1 ? 's' : ''}
      </span>
      {isLowCredits && (
        <span className="text-xs bg-red-200 text-red-800 px-1 rounded">
          Low
        </span>
      )}
    </Link>
  )
}
