'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { signIn } from '@/lib/auth'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Alert } from '@/components/ui/Alert'

export function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    const { user, error: authError } = await signIn(email, password)

    if (authError) {
      // Provide more user-friendly error messages
      if (authError.message === 'Email not confirmed') {
        setError('Your email address is not confirmed. Please check your inbox for a confirmation link. If you haven\'t received it, please check your spam folder.')
      } else {
        setError(authError.message)
      }
      setLoading(false)
    } else if (user) {
      router.push('/')
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert 
          type="error" 
          message={error} 
          onClose={() => setError(null)} 
        />
      )}
      
      <Input
        label="Email"
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
        placeholder="Enter your email"
      />
      
      <Input
        label="Password"
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        required
        placeholder="Enter your password"
      />
      
      <Button 
        type="submit" 
        loading={loading}
        className="w-full"
      >
        Sign In
      </Button>
    </form>
  )
}