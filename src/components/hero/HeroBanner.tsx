'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

interface Language {
  code: 'en' | 'th' | 'zh'
  name: string
  flag: string
}

const languages: Language[] = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'th', name: 'ไทย', flag: '🇹🇭' },
  { code: 'zh', name: '中文', flag: '🇨🇳' }
]

const translations = {
  en: {
    headline: 'Get 20 Free Credits - New Users Only',
    subheadline: 'Instant credit activation upon registration. Start processing documents immediately!',
    cta: 'Claim Your Free Credits Now',
    offerExpires: 'Offer expires in:',
    howItWorks: 'How It Works',
    steps: [
      'Sign up with email/phone',
      'Verify your account',
      'Receive 20 credits automatically',
      'Start using credits within 30 days'
    ],
    trustIndicators: {
      secure: 'Secure Payment',
      users: '10,000+ Happy Users',
      rating: '4.9/5 Rating'
    },
    videoAlt: 'Demo video showing registration and credit usage process'
  },
  th: {
    headline: 'รับ 20 เครดิตฟรี - สำหรับผู้ใช้ใหม่เท่านั้น',
    subheadline: 'เปิดใช้งานเครดิตทันทีเมื่อลงทะเบียน เริ่มประมวลผลเอกสารได้ทันที!',
    cta: 'รับเครดิตฟรีของคุณตอนนี้',
    offerExpires: 'ข้อเสนอหมดอายุใน:',
    howItWorks: 'วิธีการใช้งาน',
    steps: [
      'ลงทะเบียนด้วยอีเมล/โทรศัพท์',
      'ยืนยันบัญชีของคุณ',
      'รับ 20 เครดิตโดยอัตโนมัติ',
      'เริ่มใช้เครดิตภายใน 30 วัน'
    ],
    trustIndicators: {
      secure: 'การชำระเงินที่ปลอดภัย',
      users: 'ผู้ใช้มากกว่า 10,000 คน',
      rating: 'คะแนน 4.9/5'
    },
    videoAlt: 'วิดีโอสาธิตการลงทะเบียนและการใช้เครดิต'
  },
  zh: {
    headline: '获得20个免费积分 - 仅限新用户',
    subheadline: '注册后立即激活积分。立即开始处理文档！',
    cta: '立即领取免费积分',
    offerExpires: '优惠到期时间：',
    howItWorks: '使用方法',
    steps: [
      '使用邮箱/手机注册',
      '验证您的账户',
      '自动获得20个积分',
      '在30天内开始使用积分'
    ],
    trustIndicators: {
      secure: '安全支付',
      users: '10,000+满意用户',
      rating: '4.9/5评分'
    },
    videoAlt: '演示注册和积分使用流程的视频'
  }
}

export function HeroBanner() {
  const { user } = useAuth()
  const [currentLang, setCurrentLang] = useState<'en' | 'th' | 'zh'>('en')
  const [timeLeft, setTimeLeft] = useState({
    days: 7,
    hours: 23,
    minutes: 59,
    seconds: 59
  })
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  // Auto-detect browser language
  useEffect(() => {
    const browserLang = navigator.language.toLowerCase()
    if (browserLang.includes('th')) {
      setCurrentLang('th')
    } else if (browserLang.includes('zh')) {
      setCurrentLang('zh')
    } else {
      setCurrentLang('en')
    }
  }, [])

  // Countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 }
        } else if (prev.days > 0) {
          return { ...prev, days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 }
        }
        return prev
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const t = translations[currentLang]

  if (user) {
    return null // Don't show banner to existing users
  }

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 text-white">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>
      </div>

      {/* Language Selector */}
      <div className="absolute top-4 right-4 z-20">
        <div className="flex space-x-2">
          {languages.map((lang) => (
            <button
              key={lang.code}
              onClick={() => setCurrentLang(lang.code)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-all ${
                currentLang === lang.code
                  ? 'bg-white text-blue-600 shadow-lg'
                  : 'bg-white/20 hover:bg-white/30'
              }`}
            >
              {lang.flag} {lang.name}
            </button>
          ))}
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[600px] py-12">
          {/* Left Column - Main Content */}
          <div className="space-y-8">
            {/* Headline */}
            <div className="space-y-4">
              <div className="inline-flex items-center px-4 py-2 bg-yellow-400 text-yellow-900 rounded-full text-sm font-semibold animate-pulse">
                🎉 Limited Time Offer
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                {t.headline}
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
                {t.subheadline}
              </p>
            </div>

            {/* Countdown Timer */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <p className="text-lg font-medium mb-4">{t.offerExpires}</p>
              <div className="grid grid-cols-4 gap-4">
                {[
                  { label: 'Days', value: timeLeft.days },
                  { label: 'Hours', value: timeLeft.hours },
                  { label: 'Min', value: timeLeft.minutes },
                  { label: 'Sec', value: timeLeft.seconds }
                ].map((item, index) => (
                  <div key={index} className="text-center">
                    <div className="bg-white text-blue-600 rounded-lg p-3 font-bold text-2xl">
                      {item.value.toString().padStart(2, '0')}
                    </div>
                    <div className="text-sm mt-1 text-blue-200">{item.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* CTA Button */}
            <div className="space-y-4">
              <Link href="/signup">
                <Button 
                  size="lg" 
                  className="w-full sm:w-auto bg-yellow-400 hover:bg-yellow-500 text-yellow-900 font-bold text-lg px-8 py-4 rounded-xl shadow-2xl transform hover:scale-105 transition-all duration-200"
                >
                  {t.cta} →
                </Button>
              </Link>
              
              {/* Trust Indicators */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-blue-200">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>{t.trustIndicators.secure}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span>{t.trustIndicators.rating}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                  </svg>
                  <span>{t.trustIndicators.users}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Video & How It Works */}
          <div className="space-y-8">
            {/* Demo Video */}
            <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="relative aspect-video bg-gray-900 rounded-xl overflow-hidden">
                {!isVideoPlaying ? (
                  <div 
                    className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center cursor-pointer group"
                    onClick={() => setIsVideoPlaying(true)}
                  >
                    <div className="text-center">
                      <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto group-hover:bg-white/30 transition-colors">
                        <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z" />
                        </svg>
                      </div>
                      <p className="text-white font-medium">Watch Demo</p>
                      <p className="text-blue-200 text-sm">30 seconds</p>
                    </div>
                    <div className="absolute inset-0 bg-black/20"></div>
                    <img 
                      src="https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=800" 
                      alt={t.videoAlt}
                      className="absolute inset-0 w-full h-full object-cover opacity-30"
                    />
                  </div>
                ) : (
                  <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                      <p>Loading demo video...</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* How It Works */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h3 className="text-2xl font-bold mb-6">{t.howItWorks}</h3>
              <div className="space-y-4">
                {t.steps.map((step, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-yellow-400 text-yellow-900 rounded-full flex items-center justify-center font-bold text-sm">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <p className="text-lg">{step}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Credit Animation */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-lg font-medium">Your Credits</p>
                  <p className="text-3xl font-bold text-yellow-400">20</p>
                </div>
                <div className="relative">
                  <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
                    <svg className="w-8 h-8 text-yellow-900" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-green-900">+</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="relative block w-full h-16">
          <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" className="fill-white"></path>
        </svg>
      </div>
    </div>
  )
}