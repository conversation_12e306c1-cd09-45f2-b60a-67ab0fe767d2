export interface StripeProduct {
  id: string;
  priceId: string;
  name: string;
  description: string;
  mode: 'payment' | 'subscription';
  price?: number;
  currency?: string;
  credits?: number;
}

export interface CreditPackage {
  id: string;
  priceId: string;
  name: string;
  description: string;
  credits: number;
  price: number;
  currency: string;
  popular?: boolean;
  savings?: string;
}

export const creditPackages: CreditPackage[] = [
  {
    id: 'credits_10',
    priceId: 'price_1Rg2OM042V10vrfTwKa10siF',
    name: '10 Credits',
    description: 'Perfect for trying out OCR AI',
    credits: 10,
    price: 4.99,
    currency: 'USD'
  },
  {
    id: 'credits_50',
    priceId: 'price_1Rg2Oa042V10vrfTpWEQOM2h',
    name: '50 Credits',
    description: 'Great for small projects',
    credits: 50,
    price: 19.99,
    currency: 'USD',
    popular: true,
    savings: '20% OFF'
  },
  {
    id: 'credits_100',
    priceId: 'price_1Rg2On042V10vrfTlw6Bb9pz',
    name: '100 Credits',
    description: 'Best value for regular users',
    credits: 100,
    price: 34.99,
    currency: 'USD',
    savings: '30% OFF'
  },
  {
    id: 'credits_500',
    priceId: 'price_1Rg2P0042V10vrfTM2yLMNDC',
    name: '500 Credits',
    description: 'For power users and businesses',
    credits: 500,
    price: 149.99,
    currency: 'USD',
    savings: '40% OFF'
  }
];

export const stripeProducts: StripeProduct[] = [
  ...creditPackages.map(pkg => ({
    id: pkg.id,
    priceId: pkg.priceId,
    name: pkg.name,
    description: pkg.description,
    mode: 'payment' as const,
    price: pkg.price,
    currency: pkg.currency,
    credits: pkg.credits
  }))
];

export function getProductByPriceId(priceId: string): StripeProduct | undefined {
  return stripeProducts.find(product => product.priceId === priceId);
}

export function getProductById(id: string): StripeProduct | undefined {
  return stripeProducts.find(product => product.id === id);
}