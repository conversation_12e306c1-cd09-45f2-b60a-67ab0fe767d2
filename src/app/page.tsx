"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useSubscription } from '@/hooks/useSubscription'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Alert } from '@/components/ui/Alert'
import { HeroBanner } from '@/components/hero/HeroBanner'
import { ModernUploadArea } from '@/components/upload/ModernUploadArea'

export default function Home() {
  const { user } = useAuth()
  const { subscription } = useSubscription()
  const [file, setFile] = useState<File | null>(null)
  const [result, setResult] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedLanguage, setSelectedLanguage] = useState('auto')

  // Listen for language changes from navigation
  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      setSelectedLanguage(event.detail)
    }

    window.addEventListener('languageChanged', handleLanguageChange as EventListener)

    // Load initial language from localStorage
    const savedLanguage = localStorage.getItem('ocr-language') || 'auto'
    setSelectedLanguage(savedLanguage)

    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener)
    }
  }, [])



  const hasActiveSubscription = subscription && ['active', 'trialing'].includes(subscription.subscription_status)

  return (
    <div className="space-y-0">
      {/* Hero Banner - Only show to non-authenticated users */}
      <HeroBanner />

      <div className="flex justify-center items-center flex-col space-y-8 py-16">
        <div className="text-center max-w-3xl">
          <h1 className="text-4xl font-bold text-text-dark mb-4">
            Smart OCR & Invoice Processing
          </h1>
          <p className="text-xl text-text-light mb-8">
            Extract text from images, process invoices, and translate content with AI-powered accuracy
          </p>
          
          {!user && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-medium text-blue-900 mb-2">Get Started Today</h2>
              <p className="text-blue-800 mb-4">
                Sign up for free to start processing your documents with our advanced OCR technology.
              </p>
              <div className="space-x-4">
                <Link href="/signup">
                  <Button>Create Free Account</Button>
                </Link>
                <Link href="/login">
                  <Button variant="outline">Sign In</Button>
                </Link>
              </div>
            </div>
          )}

          {user && !hasActiveSubscription && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-medium text-yellow-900 mb-2">Upgrade to Pro</h2>
              <p className="text-yellow-800 mb-4">
                Get unlimited OCR processing, invoice extraction, and premium features.
              </p>
              <Link href="/pricing">
                <Button>View Plans</Button>
              </Link>
            </div>
          )}

          {user && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-medium text-green-900 mb-2">Ready to Extract Text?</h2>
              <p className="text-green-800 mb-4">
                Visit our dedicated upload page for a focused OCR experience with better file management and result handling.
              </p>
              <div className="space-x-3">
                <Link href="/upload">
                  <Button>Go to Upload Page</Button>
                </Link>
                <span className="text-green-700 text-sm">Or use the upload area below</span>
              </div>
            </div>
          )}
        </div>

        <div className="w-full max-w-4xl">
          <ModernUploadArea
            onFileSelect={setFile}
            onUploadStart={() => {
              setLoading(true)
              setResult(null)
              setError(null)
            }}
            onUploadComplete={(data) => {
              setResult(data)
              setLoading(false)
            }}
            onUploadError={(errorMsg) => {
              setError(errorMsg)
              setLoading(false)
            }}
            loading={loading}
            selectedLanguage={selectedLanguage}
          />

          {result && (
            <div className="mt-8 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">OCR Result</h2>
                <p className="text-sm text-gray-600 mt-1">
                  Extracted text from your image
                </p>
              </div>
              <div className="p-6">
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                    {typeof result === 'string' ? result : JSON.stringify(result, null, 2)}
                  </pre>
                </div>
                <div className="mt-4 flex space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const text = typeof result === 'string' ? result : JSON.stringify(result, null, 2)
                      navigator.clipboard.writeText(text)
                    }}
                  >
                    📋 Copy Text
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setResult(null)}
                  >
                    🗑️ Clear
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Features Section */}
        <div className="w-full max-w-6xl mt-16">
          <h2 className="text-3xl font-bold text-center text-text-dark mb-12">
            Powerful Features
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-text-dark mb-2">OCR Processing</h3>
              <p className="text-text-light">
                Extract text from images with high accuracy using advanced AI models
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-text-dark mb-2">Invoice Extraction</h3>
              <p className="text-text-light">
                Automatically extract structured data from invoices and receipts
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-text-dark mb-2">Multi-Language</h3>
              <p className="text-text-light">
                Support for Thai, English, and Chinese with translation capabilities
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}