"use client"

import { useState, ChangeEvent } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useSubscription } from '@/hooks/useSubscription'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Alert } from '@/components/ui/Alert'
import { HeroBanner } from '@/components/hero/HeroBanner'

export default function Home() {
  const { user } = useAuth()
  const { subscription } = useSubscription()
  const [file, setFile] = useState<File | null>(null)
  const [result, setResult] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files
    if (fileList) {
      setFile(fileList[0])
      setError(null)
    }
  }

  const handleUpload = async () => {
    if (!file) return

    if (!user) {
      setError('Please sign in to use OCR features')
      return
    }

    setLoading(true)
    setResult(null)
    setError(null)

    try {
      const formData = new FormData()
      formData.append('image', file)

      const response = await fetch('/api/ocr', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        setResult(data.data)
      } else {
        setError(data.error || 'OCR processing failed')
      }
    } catch (error) {
      setError('Upload failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const hasActiveSubscription = subscription && ['active', 'trialing'].includes(subscription.subscription_status)

  return (
    <div className="space-y-0">
      {/* Hero Banner - Only show to non-authenticated users */}
      <HeroBanner />

      <div className="flex justify-center items-center flex-col space-y-8 py-16">
        <div className="text-center max-w-3xl">
          <h1 className="text-4xl font-bold text-text-dark mb-4">
            Smart OCR & Invoice Processing
          </h1>
          <p className="text-xl text-text-light mb-8">
            Extract text from images, process invoices, and translate content with AI-powered accuracy
          </p>
          
          {!user && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-medium text-blue-900 mb-2">Get Started Today</h2>
              <p className="text-blue-800 mb-4">
                Sign up for free to start processing your documents with our advanced OCR technology.
              </p>
              <div className="space-x-4">
                <Link href="/signup">
                  <Button>Create Free Account</Button>
                </Link>
                <Link href="/login">
                  <Button variant="outline">Sign In</Button>
                </Link>
              </div>
            </div>
          )}

          {user && !hasActiveSubscription && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-medium text-yellow-900 mb-2">Upgrade to Pro</h2>
              <p className="text-yellow-800 mb-4">
                Get unlimited OCR processing, invoice extraction, and premium features.
              </p>
              <Link href="/pricing">
                <Button>View Plans</Button>
              </Link>
            </div>
          )}
        </div>

        <div className="w-full max-w-2xl">
          <div className="upload-area relative">
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <p className="text-lg text-gray-600">
                {file ? file.name : 'Drag and drop an image, or click to select a file'}
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Supports JPEG, PNG, and WebP formats (max 10MB)
              </p>
            </div>
          </div>

          {error && (
            <Alert 
              type="error" 
              message={error} 
              onClose={() => setError(null)} 
            />
          )}

          <div className="text-center mt-6">
            <Button
              onClick={handleUpload}
              disabled={!file || loading || !user}
              loading={loading}
              size="lg"
            >
              {loading ? 'Processing...' : 'Extract Text'}
            </Button>
          </div>

          {result && (
            <div className="result-card mt-8">
              <h2 className="text-xl font-semibold mb-4">OCR Result</h2>
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm text-gray-800">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Features Section */}
        <div className="w-full max-w-6xl mt-16">
          <h2 className="text-3xl font-bold text-center text-text-dark mb-12">
            Powerful Features
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-text-dark mb-2">OCR Processing</h3>
              <p className="text-text-light">
                Extract text from images with high accuracy using advanced AI models
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-text-dark mb-2">Invoice Extraction</h3>
              <p className="text-text-light">
                Automatically extract structured data from invoices and receipts
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-text-dark mb-2">Multi-Language</h3>
              <p className="text-text-light">
                Support for Thai, English, and Chinese with translation capabilities
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}