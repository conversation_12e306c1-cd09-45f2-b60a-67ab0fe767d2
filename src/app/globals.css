@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Custom component styles */
.upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-colors hover:border-primary-blue;
}

.upload-area.dragover {
  @apply border-primary-blue bg-blue-50;
}

.result-card {
  @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue;
}

/* Hero Banner Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Responsive video container */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Custom scrollbar for result cards */
.result-card::-webkit-scrollbar {
  width: 6px;
}

.result-card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.result-card::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.result-card::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Language selector hover effects */
.language-selector button {
  transition: all 0.2s ease-in-out;
}

.language-selector button:hover {
  transform: translateY(-2px);
}

/* Countdown timer styling */
.countdown-digit {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Trust indicators styling */
.trust-indicator {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .hero-banner {
    min-height: 500px;
  }
  
  .hero-content {
    padding: 2rem 1rem;
  }
  
  .countdown-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .language-selector {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 1rem;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hero-banner {
    background-image: 
      linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(147, 51, 234, 0.9) 50%, rgba(79, 70, 229, 0.9) 100%),
      url('data:image/svg+xml,%3Csvg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="60" cy="60" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  }
}

/* Print styles */
@media print {
  .hero-banner {
    display: none;
  }
}