'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Alert } from '@/components/ui/Alert'
import { Button } from '@/components/ui/Button'

export default function SuccessPage() {
  const searchParams = useSearchParams()
  const sessionId = searchParams.get('session_id')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Simulate a brief loading period to show the success state
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto py-12 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Processing your payment...</h1>
        <p className="text-gray-600">Please wait while we confirm your subscription.</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto py-12">
        <Alert type="error" message={error} />
        <div className="text-center mt-6">
          <Link href="/pricing">
            <Button>Back to Pricing</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto py-12 text-center">
      <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      </div>
      
      <h1 className="text-3xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
      <p className="text-xl text-gray-600 mb-8">
        Thank you for your subscription. Your account has been activated and you now have access to all premium features.
      </p>

      {sessionId && (
        <div className="bg-gray-50 rounded-lg p-4 mb-8">
          <p className="text-sm text-gray-600">
            <span className="font-medium">Session ID:</span> {sessionId}
          </p>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <Link href="/">
            <Button size="lg">Get Started</Button>
          </Link>
        </div>
        <div>
          <Link href="/account" className="text-primary-blue hover:underline">
            View your account details
          </Link>
        </div>
      </div>

      <div className="mt-12 bg-blue-50 rounded-lg p-6">
        <h2 className="text-lg font-medium text-blue-900 mb-2">What's Next?</h2>
        <ul className="text-left text-blue-800 space-y-2">
          <li>• Upload your first document for OCR processing</li>
          <li>• Explore invoice extraction features</li>
          <li>• Try our multi-language translation tools</li>
          <li>• Access your subscription dashboard</li>
        </ul>
      </div>
    </div>
  )
}