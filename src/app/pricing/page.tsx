'use client'

import { useAuth } from '@/hooks/useAuth'
import { SubscriptionCard } from '@/components/subscription/SubscriptionCard'
import { creditPackages } from '@/stripe-config'
import Link from 'next/link'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function PricingPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()

  // Redirect authenticated users to credits dashboard
  useEffect(() => {
    if (!authLoading && user) {
      router.push('/dashboard/credits')
    }
  }, [user, authLoading, router])

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    )
  }

  if (user) {
    // This will be redirected, but show loading state briefly
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">OCR AI Credit Packages</h1>
        <p className="text-xl text-gray-600 mb-8">
          Pay-as-you-go credits for powerful OCR and document processing
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <p className="text-blue-800">
            Please{' '}
            <Link href="/login" className="font-medium underline">
              sign in
            </Link>{' '}
            or{' '}
            <Link href="/signup" className="font-medium underline">
              create an account
            </Link>{' '}
            to purchase credits.
          </p>
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {creditPackages.map((pkg) => (
          <SubscriptionCard
            key={pkg.id}
            product={{
              id: pkg.id,
              priceId: pkg.priceId,
              name: pkg.name,
              description: pkg.description,
              mode: 'payment' as const,
              price: pkg.price,
              currency: pkg.currency,
              credits: pkg.credits
            }}
          />
        ))}
      </div>

      <div className="text-center mt-8">
        <p className="text-gray-600">
          Need help choosing? <Link href="/contact" className="text-primary-blue hover:underline">Contact us</Link>
        </p>
      </div>

      {/* Features Section */}
      <div className="mt-16 bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why Choose OCR AI Credits?</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl mb-3">💳</div>
            <h3 className="font-semibold mb-2">Pay As You Go</h3>
            <p className="text-gray-600 text-sm">No monthly subscriptions. Only pay for what you use.</p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-3">⏰</div>
            <h3 className="font-semibold mb-2">Never Expire</h3>
            <p className="text-gray-600 text-sm">Your credits never expire. Use them whenever you need.</p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-3">🚀</div>
            <h3 className="font-semibold mb-2">Instant Processing</h3>
            <p className="text-gray-600 text-sm">Fast, accurate OCR processing powered by advanced AI.</p>
          </div>
        </div>
      </div>
    </div>
  )
}