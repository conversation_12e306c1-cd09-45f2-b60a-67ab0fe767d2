'use client'

import { useAuth } from '@/hooks/useAuth'
import { useSubscription } from '@/hooks/useSubscription'
import { SubscriptionCard } from '@/components/subscription/SubscriptionCard'
import { stripeProducts } from '@/stripe-config'
import Link from 'next/link'

export default function PricingPage() {
  const { user, loading: authLoading } = useAuth()
  const { subscription, loading: subLoading } = useSubscription()

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
          <p className="text-xl text-gray-600 mb-8">
            Get started with our powerful OCR and invoice processing tools
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <p className="text-blue-800">
              Please{' '}
              <Link href="/login" className="font-medium underline">
                sign in
              </Link>{' '}
              or{' '}
              <Link href="/signup" className="font-medium underline">
                create an account
              </Link>{' '}
              to subscribe to a plan.
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stripeProducts.map((product) => (
            <SubscriptionCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    )
  }

  const currentPriceId = subscription?.price_id

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
        <p className="text-xl text-gray-600">
          Get started with our powerful OCR and invoice processing tools
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {stripeProducts.map((product) => (
          <SubscriptionCard 
            key={product.id} 
            product={product}
            isCurrentPlan={currentPriceId === product.priceId}
          />
        ))}
      </div>

      {!subLoading && (!subscription || subscription.subscription_status === 'not_started') && (
        <div className="text-center mt-8">
          <p className="text-gray-600">
            Need help choosing? <Link href="/contact" className="text-primary-blue hover:underline">Contact us</Link>
          </p>
        </div>
      )}
    </div>
  )
}