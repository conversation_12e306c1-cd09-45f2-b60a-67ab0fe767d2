'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Alert } from '@/components/ui/Alert'
import { ModernUploadArea } from '@/components/upload/ModernUploadArea'
import { LanguageSelector } from '@/components/ui/LanguageSelector'

export default function UploadPage() {
  const { user, loading: authLoading } = useAuth()
  const { credits, loading: creditsLoading } = useCredits()
  const router = useRouter()
  const [selectedLanguage, setSelectedLanguage] = useState('auto')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // Load saved language preference
  useEffect(() => {
    const savedLanguage = localStorage.getItem('ocr-language') || 'auto'
    setSelectedLanguage(savedLanguage)
  }, [])

  // Save language preference when changed
  useEffect(() => {
    localStorage.setItem('ocr-language', selectedLanguage)
    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: selectedLanguage }))
  }, [selectedLanguage])

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login')
    }
  }, [user, authLoading, router])

  const creditsRemaining = credits?.credits_remaining ?? 0
  const hasEnoughCredits = creditsRemaining >= 1

  const handleUploadComplete = (data: any) => {
    setResult(data)
    setLoading(false)
    setError(null)
  }

  const handleUploadError = (errorMsg: string) => {
    setError(errorMsg)
    setLoading(false)
    setResult(null)
  }

  const handleUploadStart = () => {
    setLoading(true)
    setResult(null)
    setError(null)
  }

  const handleClearResult = () => {
    setResult(null)
    setSelectedFile(null)
    setError(null)
  }

  const handleCopyText = () => {
    const text = typeof result === 'string' ? result : JSON.stringify(result, null, 2)
    navigator.clipboard.writeText(text)
  }

  const downloadAsText = () => {
    const text = typeof result === 'string' ? result : JSON.stringify(result, null, 2)
    const blob = new Blob([text], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ocr-result-${new Date().getTime()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (authLoading || creditsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-blue-600 hover:text-blue-700 font-medium">
                ← Back to Home
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-semibold text-gray-900">Upload & Extract Text</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Language Selector */}
              <LanguageSelector
                selectedLanguage={selectedLanguage}
                onLanguageChange={setSelectedLanguage}
              />
              
              {/* Credits Display */}
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
                creditsRemaining <= 2 
                  ? 'bg-red-100 text-red-700'
                  : creditsRemaining <= 5
                  ? 'bg-yellow-100 text-yellow-700'
                  : 'bg-green-100 text-green-700'
              }`}>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">{creditsRemaining} credits</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="space-y-8">
          {/* Page Description */}
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Extract Text from Images
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Upload images containing text and get accurate OCR results. Supports multiple languages including Thai, English, and Chinese.
            </p>
          </div>

          {/* Upload Area */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <ModernUploadArea
              onFileSelect={setSelectedFile}
              onUploadStart={handleUploadStart}
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              loading={loading}
              selectedLanguage={selectedLanguage}
            />
          </div>

          {/* Error Display */}
          {error && (
            <Alert 
              type="error" 
              message={error} 
              onClose={() => setError(null)} 
            />
          )}

          {/* Low Credits Warning */}
          {!hasEnoughCredits && (
            <Alert 
              type="warning" 
              message={`Insufficient Credits: You need at least 1 credit to process an image. Current balance: ${creditsRemaining}. Visit pricing page to purchase more credits.`}
            />
          )}

          {/* Results Section */}
          {result && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      Extracted Text
                    </h3>
                    <p className="text-blue-100 text-sm mt-1">
                      Processing completed successfully
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyText}
                      className="bg-white text-blue-600 border-white hover:bg-blue-50"
                    >
                      📋 Copy
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={downloadAsText}
                      className="bg-white text-blue-600 border-white hover:bg-blue-50"
                    >
                      💾 Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleClearResult}
                      className="bg-white text-blue-600 border-white hover:bg-blue-50"
                    >
                      🗑️ Clear
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
                    {typeof result === 'string' ? result : JSON.stringify(result, null, 2)}
                  </pre>
                </div>
                
                {/* Additional Actions */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex flex-wrap gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Process as invoice
                        router.push('/invoice')
                      }}
                    >
                      📄 Process as Invoice
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Translate text
                        router.push('/translate')
                      }}
                    >
                      🌐 Translate Text
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Save to dashboard
                        router.push('/dashboard')
                      }}
                    >
                      💾 Save to Dashboard
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tips & Guidelines */}
          <div className="bg-blue-50 rounded-xl border border-blue-200 p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              📝 Tips for Better Results
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="text-blue-600 mt-0.5">•</span>
                  <span>Use high-quality, well-lit images</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-600 mt-0.5">•</span>
                  <span>Ensure text is clearly visible and not blurry</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-600 mt-0.5">•</span>
                  <span>Avoid shadows or reflections on the text</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="text-blue-600 mt-0.5">•</span>
                  <span>Crop images to focus on text areas</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-600 mt-0.5">•</span>
                  <span>Select the correct language for better accuracy</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-600 mt-0.5">•</span>
                  <span>Maximum file size: 10MB</span>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Highlights */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-white rounded-xl border border-gray-200">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">High Accuracy</h3>
              <p className="text-sm text-gray-600">Advanced AI models ensure accurate text extraction from your images</p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl border border-gray-200">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Multi-Language</h3>
              <p className="text-sm text-gray-600">Support for Thai, English, Chinese, and auto-detection</p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl border border-gray-200">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Fast Processing</h3>
              <p className="text-sm text-gray-600">Get your results in seconds with our optimized processing pipeline</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
