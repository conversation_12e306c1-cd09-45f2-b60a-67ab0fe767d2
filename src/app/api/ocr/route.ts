import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { OCRService } from '@/lib/ocr'
import { serverCreditsService } from '@/lib/credits'
import { rateLimiter, getRateLimitHeaders } from '@/lib/rateLimit'
import { APIResponse, OCRResult } from '@/types'

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.ip || 'unknown'
    const rateLimitInfo = rateLimiter.check(clientIP)

    const supabase = createRouteHandlerClient({ cookies })
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      )
    }

    const hasEnoughCredits = await serverCreditsService.hasEnoughCredits(user.id)

    if (!hasEnoughCredits) {
      return NextResponse.json(
        {
          success: false,
          error: 'Insufficient credits',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    if (!rateLimitInfo.allowed) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Rate limit exceeded',
          timestamp: new Date().toISOString()
        },
        { 
          status: 429,
          headers: getRateLimitHeaders(rateLimitInfo)
        }
      )
    }

    const formData = await request.formData()
    const file = formData.get('image') as File

    if (!file) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'No image file provided',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'File too large. Maximum size is 10MB',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Invalid file type. Only JPEG, PNG, and WebP are supported',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    // Create upload record first
    const uploadRecord = await serverCreditsService.createUpload({
      user_id: user.id,
      filename: file.name,
      file_type: file.type,
      file_size: file.size,
      status: 'processing',
      credits_used: 1
    })

    if (!uploadRecord) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Error creating upload record',
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

    // Deduct credits before processing
    const deducted = await serverCreditsService.deductCredits(user.id, uploadRecord.id, 1)

    if (!deducted) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Error deducting credits',
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

    try {
      // Process the image
      const imageBuffer = Buffer.from(await file.arrayBuffer())
      const ocrService = new OCRService(process.env.TOGETHER_API_KEY!)

      const result = await ocrService.processImage(imageBuffer, {
        languages: ['th', 'en', 'zh'],
        detectHandwriting: true
      })

      // Update upload status to done
      await serverCreditsService.updateUploadStatus(uploadRecord.id, 'done', {
        ai_description: result.extractedText
      })

      return NextResponse.json<APIResponse<OCRResult>>(
        {
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        },
        { 
          status: 200,
          headers: getRateLimitHeaders(rateLimitInfo)
        }
      )
    } catch (ocrError) {
      // Mark upload as error
      await serverCreditsService.updateUploadStatus(uploadRecord.id, 'error')
      throw ocrError
    }
  } catch (error) {
    console.error('OCR processing error:', error)
    
    return NextResponse.json<APIResponse<null>>(
      {
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
