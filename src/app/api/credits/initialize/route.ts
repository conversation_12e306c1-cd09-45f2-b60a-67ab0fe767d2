import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { serverCreditsService } from '@/lib/credits'

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Initialize credits for the user (backup method)
    const success = await serverCreditsService.initializeUserCredits(user.id)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to initialize credits' },
        { status: 500 }
      )
    }

    // Get the user's current credits
    const credits = await serverCreditsService.getUserCredits(user.id)
    
    return NextResponse.json({
      success: true,
      credits: credits?.credits_remaining || 0,
      message: 'Credits initialized successfully'
    })

  } catch (error) {
    console.error('Error initializing credits:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
