import { NextRequest, NextResponse } from 'next/server'
import { rateLimiter, getRateLimitHeaders } from '@/lib/rateLimit'
import { APIResponse, InvoiceData } from '@/types'

// Mock invoice extraction service
class InvoiceExtractor {
  async extractFromText(ocrText: string): Promise<InvoiceData> {
    // Mock extraction - replace with actual AI processing
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      invoiceNumber: "INV-2025-001",
      date: "2025-07-01",
      billingName: "Sample Company Ltd.",
      billingAddress: "123 Business Street, Bangkok, Thailand",
      items: [
        {
          description: "Product A",
          quantity: 2,
          unitPrice: 100,
          lineTotal: 200
        },
        {
          description: "Service B",
          quantity: 1,
          unitPrice: 300,
          lineTotal: 300
        }
      ],
      subtotal: 500,
      taxRate: 7,
      total: 535,
      currency: "THB"
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    const rateLimitInfo = rateLimiter.check(clientIP)

    if (!rateLimiter.isAllowed(clientIP)) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Rate limit exceeded',
          timestamp: new Date().toISOString()
        },
        { 
          status: 429,
          headers: getRateLimitHeaders(rateLimitInfo)
        }
      )
    }

    const body = await request.json()

    if (!body.ocrText) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Missing required field: ocrText',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    const extractor = new InvoiceExtractor()
    const result = await extractor.extractFromText(body.ocrText)

    return NextResponse.json<APIResponse<InvoiceData>>(
      {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      },
      { 
        status: 200,
        headers: getRateLimitHeaders(rateLimitInfo)
      }
    )
  } catch (error) {
    console.error('Invoice extraction error:', error)
    
    return NextResponse.json<APIResponse<null>>(
      {
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
