import { NextRequest, NextResponse } from 'next/server'
import { TranslationService } from '@/lib/translation'
import { rateLimiter, getRateLimitHeaders } from '@/lib/rateLimit'
import { APIResponse, TranslationRequest, TranslationResult } from '@/types'

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    const rateLimitInfo = rateLimiter.check(clientIP)

    if (!rateLimiter.isAllowed(clientIP)) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Rate limit exceeded',
          timestamp: new Date().toISOString()
        },
        { 
          status: 429,
          headers: getRateLimitHeaders(rateLimitInfo)
        }
      )
    }

    const body: TranslationRequest = await request.json()

    // Validate request body
    if (!body.text || !body.sourceLang || !body.targetLang) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Missing required fields: text, sourceLang, targetLang',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    // Validate language codes
    const supportedLangs = ['th', 'en', 'zh']
    if (!supportedLangs.includes(body.sourceLang) || !supportedLangs.includes(body.targetLang)) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Unsupported language. Supported languages: th, en, zh',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    // Validate text length (max 10,000 characters)
    if (body.text.length > 10000) {
      return NextResponse.json<APIResponse<null>>(
        {
          success: false,
          error: 'Text too long. Maximum length is 10,000 characters',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    const translationService = new TranslationService(process.env.OPENROUTER_API_KEY!)
    const result = await translationService.translate(body)

    return NextResponse.json<APIResponse<TranslationResult>>(
      {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      },
      { 
        status: 200,
        headers: getRateLimitHeaders(rateLimitInfo)
      }
    )
  } catch (error) {
    console.error('Translation error:', error)
    
    return NextResponse.json<APIResponse<null>>(
      {
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
