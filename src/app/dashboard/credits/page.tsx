'use client'

import { CreditsOverview } from '@/components/dashboard/CreditsOverview'
import { useCredits } from '@/hooks/useCredits'
import { useAuth } from '@/hooks/useAuth'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { creditsService, CreditTransaction, Upload } from '@/lib/credits'

// Remove duplicate interfaces since they're imported from credits service

export default function CreditsPage() {
  const { user } = useAuth()
  const { credits, loading, error, refreshCredits } = useCredits()
  const [transactions, setTransactions] = useState<CreditTransaction[]>([])
  const [uploads, setUploads] = useState<Upload[]>([])
  const [showTransactions, setShowTransactions] = useState(false)
  const [showUploads, setShowUploads] = useState(false)
  const [transactionsLoading, setTransactionsLoading] = useState(false)
  const [uploadsLoading, setUploadsLoading] = useState(false)

  const fetchTransactions = async () => {
    if (!user) return

    try {
      setTransactionsLoading(true)
      const transactionData = await creditsService.getCreditTransactions(user.id, 20)
      setTransactions(transactionData)
      setShowTransactions(!showTransactions)
    } catch (error) {
      console.error('Error fetching transactions:', error)
    } finally {
      setTransactionsLoading(false)
    }
  }

  const fetchUploads = async () => {
    if (!user) return

    try {
      setUploadsLoading(true)
      const uploadData = await creditsService.getUserUploads(user.id, 20)
      setUploads(uploadData)
      setShowUploads(!showUploads)
    } catch (error) {
      console.error('Error fetching uploads:', error)
    } finally {
      setUploadsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'purchase':
        return '💳'
      case 'usage':
        return '📄'
      case 'refund':
        return '↩️'
      default:
        return '📊'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'done':
        return '✅'
      case 'processing':
        return '⏳'
      case 'error':
        return '❌'
      case 'pending':
        return '⏸️'
      default:
        return '❓'
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-red-600 mb-2">Error Loading Credits</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={refreshCredits}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Credits Dashboard</h1>
        <p className="text-gray-600">Manage your OCR AI credits and view usage history</p>
      </div>

      {/* Credits Overview */}
      <CreditsOverview />

      {/* Quick Actions */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Transactions</h3>
          <p className="text-gray-600 mb-4">View your credit purchase and usage history</p>
          <Button
            onClick={fetchTransactions}
            variant="outline"
            disabled={transactionsLoading}
          >
            {transactionsLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                <span>Loading...</span>
              </div>
            ) : (
              `${showTransactions ? 'Hide' : 'View'} Transactions`
            )}
          </Button>

          {showTransactions && (
            <div className="mt-4">
              {transactions.length === 0 ? (
                <div className="text-sm text-gray-500 text-center py-4">
                  No transactions found
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-xl">
                          {getTransactionIcon(transaction.transaction_type)}
                        </span>
                        <div>
                          <div className="font-medium">
                            {transaction.transaction_type === 'purchase' ? 'Credit Purchase' :
                             transaction.transaction_type === 'usage' ? 'OCR Processing' :
                             'Credit Refund'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatDate(transaction.created_at)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-semibold ${
                          transaction.transaction_type === 'purchase' ? 'text-green-600' :
                          transaction.transaction_type === 'usage' ? 'text-red-600' :
                          'text-blue-600'
                        }`}>
                          {transaction.transaction_type === 'usage' ? '-' : '+'}
                          {Math.abs(transaction.amount)} credits
                        </div>
                        {transaction.stripe_payment_intent_id && (
                          <div className="text-xs text-gray-400">
                            Payment ID: {transaction.stripe_payment_intent_id.slice(-8)}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Uploads</h3>
          <p className="text-gray-600 mb-4">Track your OCR processing history and status</p>
          <Button
            onClick={fetchUploads}
            variant="outline"
            disabled={uploadsLoading}
          >
            {uploadsLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                <span>Loading...</span>
              </div>
            ) : (
              `${showUploads ? 'Hide' : 'View'} Upload History`
            )}
          </Button>

          {showUploads && (
            <div className="mt-4">
              {uploads.length === 0 ? (
                <div className="text-sm text-gray-500 text-center py-4">
                  No uploads found
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {uploads.map((upload) => (
                    <div
                      key={upload.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-xl">
                          {getStatusIcon(upload.status)}
                        </span>
                        <div>
                          <div className="font-medium truncate max-w-xs">
                            {upload.filename}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatDate(upload.created_at)} • {upload.file_type}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-700">
                          {upload.credits_used} credit{upload.credits_used !== 1 ? 's' : ''}
                        </div>
                        <div className={`text-xs px-2 py-1 rounded-full ${
                          upload.status === 'done' ? 'bg-green-100 text-green-800' :
                          upload.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                          upload.status === 'error' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {upload.status}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="mt-8 bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Usage Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {credits?.credits_remaining || 0}
            </div>
            <div className="text-sm text-gray-600">Credits Remaining</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {credits?.total_credits_purchased || 0}
            </div>
            <div className="text-sm text-gray-600">Total Purchased</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {credits ? credits.total_credits_purchased - credits.credits_remaining : 0}
            </div>
            <div className="text-sm text-gray-600">Credits Used</div>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4 text-blue-900">💡 Tips for Managing Credits</h3>
        <ul className="space-y-2 text-blue-800">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Each OCR operation costs 1 credit regardless of image complexity</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Credits never expire - purchase in bulk to save money</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Failed OCR operations will still deduct credits - ensure good image quality</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Monitor your usage patterns to optimize credit purchases</span>
          </li>
        </ul>
      </div>
    </div>
  )
}
