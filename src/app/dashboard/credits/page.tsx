'use client'

import { CreditsOverview } from '@/components/dashboard/CreditsOverview'
import { useCredits } from '@/hooks/useCredits'
import { useAuth } from '@/hooks/useAuth'
import { useState } from 'react'
import { Button } from '@/components/ui/Button'

interface CreditTransaction {
  id: string
  amount: number
  transaction_type: 'purchase' | 'usage' | 'refund'
  stripe_payment_intent_id?: string
  upload_id?: string
  created_at: string
}

interface Upload {
  id: string
  filename: string
  file_type: string
  file_size: number
  status: 'pending' | 'processing' | 'done' | 'error'
  credits_used: number
  created_at: string
}

export default function CreditsPage() {
  const { user } = useAuth()
  const { credits, loading, error, refreshCredits } = useCredits()
  const [transactions, setTransactions] = useState<CreditTransaction[]>([])
  const [uploads, setUploads] = useState<Upload[]>([])
  const [showTransactions, setShowTransactions] = useState(false)
  const [showUploads, setShowUploads] = useState(false)

  const fetchTransactions = async () => {
    if (!user) return
    
    try {
      // This would use creditsService.getCreditTransactions in a real implementation
      // For now, we'll just show the UI structure
      setShowTransactions(!showTransactions)
    } catch (error) {
      console.error('Error fetching transactions:', error)
    }
  }

  const fetchUploads = async () => {
    if (!user) return
    
    try {
      // This would use creditsService.getUserUploads in a real implementation
      // For now, we'll just show the UI structure
      setShowUploads(!showUploads)
    } catch (error) {
      console.error('Error fetching uploads:', error)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-red-600 mb-2">Error Loading Credits</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={refreshCredits}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Credits Dashboard</h1>
        <p className="text-gray-600">Manage your OCR AI credits and view usage history</p>
      </div>

      {/* Credits Overview */}
      <CreditsOverview />

      {/* Quick Actions */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Transactions</h3>
          <p className="text-gray-600 mb-4">View your credit purchase and usage history</p>
          <Button onClick={fetchTransactions} variant="outline">
            {showTransactions ? 'Hide' : 'View'} Transactions
          </Button>
          
          {showTransactions && (
            <div className="mt-4 space-y-2">
              <div className="text-sm text-gray-500">
                Feature coming soon - transaction history will be displayed here
              </div>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Uploads</h3>
          <p className="text-gray-600 mb-4">Track your OCR processing history and status</p>
          <Button onClick={fetchUploads} variant="outline">
            {showUploads ? 'Hide' : 'View'} Upload History
          </Button>
          
          {showUploads && (
            <div className="mt-4 space-y-2">
              <div className="text-sm text-gray-500">
                Feature coming soon - upload history will be displayed here
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="mt-8 bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Usage Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {credits?.credits_remaining || 0}
            </div>
            <div className="text-sm text-gray-600">Credits Remaining</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {credits?.total_credits_purchased || 0}
            </div>
            <div className="text-sm text-gray-600">Total Purchased</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {credits ? credits.total_credits_purchased - credits.credits_remaining : 0}
            </div>
            <div className="text-sm text-gray-600">Credits Used</div>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4 text-blue-900">💡 Tips for Managing Credits</h3>
        <ul className="space-y-2 text-blue-800">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Each OCR operation costs 1 credit regardless of image complexity</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Credits never expire - purchase in bulk to save money</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Failed OCR operations will still deduct credits - ensure good image quality</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Monitor your usage patterns to optimize credit purchases</span>
          </li>
        </ul>
      </div>
    </div>
  )
}
