'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { useCredits } from '@/hooks/useCredits'

export default function CreditPurchaseSuccess() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { refreshCredits } = useCredits()
  const [sessionId, setSessionId] = useState<string | null>(null)

  useEffect(() => {
    const sessionIdParam = searchParams.get('session_id')
    setSessionId(sessionIdParam)
    
    // Refresh credits when the page loads to show updated balance
    if (sessionIdParam) {
      refreshCredits()
    }
  }, [searchParams, refreshCredits])

  const handleContinue = () => {
    router.push('/dashboard/credits')
  }

  const handleStartOCR = () => {
    router.push('/')
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Success Icon */}
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>

        {/* Success Message */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Payment Successful! 🎉
        </h1>
        
        <p className="text-gray-600 mb-6">
          Your credits have been added to your account and are ready to use for OCR processing.
        </p>

        {sessionId && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-500 mb-1">Transaction ID</p>
            <p className="text-xs font-mono text-gray-700 break-all">{sessionId}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button 
            onClick={handleStartOCR}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            Start Using OCR AI
          </Button>
          
          <Button 
            onClick={handleContinue}
            variant="outline"
            className="w-full"
          >
            View Credits Dashboard
          </Button>
        </div>

        {/* Additional Info */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">What's Next?</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Upload images for OCR processing</li>
            <li>• Each operation uses 1 credit</li>
            <li>• Credits never expire</li>
            <li>• Track usage in your dashboard</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
