import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'

// Types
export interface UserCredits {
  id: string
  user_id: string
  credits_remaining: number
  total_credits_purchased: number
  created_at: string
  updated_at: string
}

export interface Upload {
  id: string
  user_id: string
  filename: string
  file_type: string
  file_size: number
  status: 'pending' | 'processing' | 'done' | 'error'
  webhook_url?: string
  ai_generated_image_url?: string
  ai_description?: string
  credits_used: number
  created_at: string
  updated_at: string
}

export interface CreditTransaction {
  id: string
  user_id: string
  amount: number
  transaction_type: 'purchase' | 'usage' | 'refund'
  stripe_payment_intent_id?: string
  upload_id?: string
  created_at: string
}

export class CreditsService {
  private supabase

  constructor(useServerClient = false) {
    if (useServerClient) {
      // For server-side usage (API routes)
      this.supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      )
    } else {
      // For client-side usage
      this.supabase = createClientComponentClient()
    }
  }

  // Get user credits
  async getUserCredits(userId: string): Promise<UserCredits | null> {
    const { data, error } = await this.supabase
      .from('user_credits')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching user credits:', error)
      return null
    }

    return data
  }

  // Check if user has enough credits
  async hasEnoughCredits(userId: string, requiredCredits: number = 1): Promise<boolean> {
    const credits = await this.getUserCredits(userId)
    return credits ? credits.credits_remaining >= requiredCredits : false
  }

  // Deduct credits using the database function
  async deductCredits(userId: string, uploadId: string, creditsToDeduct: number = 1): Promise<boolean> {
    const { data, error } = await this.supabase
      .rpc('deduct_credits', {
        p_user_id: userId,
        p_upload_id: uploadId,
        p_credits_to_deduct: creditsToDeduct
      })

    if (error) {
      console.error('Error deducting credits:', error)
      return false
    }

    return data === true
  }

  // Add credits using the database function
  async addCredits(userId: string, creditsToAdd: number, stripePaymentIntentId?: string): Promise<boolean> {
    const { error } = await this.supabase
      .rpc('add_credits', {
        p_user_id: userId,
        p_credits_to_add: creditsToAdd,
        p_stripe_payment_intent_id: stripePaymentIntentId
      })

    if (error) {
      console.error('Error adding credits:', error)
      return false
    }

    return true
  }

  // Get user's credit transactions
  async getCreditTransactions(userId: string, limit: number = 10): Promise<CreditTransaction[]> {
    const { data, error } = await this.supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching credit transactions:', error)
      return []
    }

    return data || []
  }

  // Get user's uploads
  async getUserUploads(userId: string, limit: number = 10): Promise<Upload[]> {
    const { data, error } = await this.supabase
      .from('uploads')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching user uploads:', error)
      return []
    }

    return data || []
  }

  // Create upload record
  async createUpload(upload: Omit<Upload, 'id' | 'created_at' | 'updated_at'>): Promise<Upload | null> {
    const { data, error } = await this.supabase
      .from('uploads')
      .insert(upload)
      .select()
      .single()

    if (error) {
      console.error('Error creating upload:', error)
      return null
    }

    return data
  }

  // Update upload status
  async updateUploadStatus(
    uploadId: string, 
    status: Upload['status'],
    additionalData?: Partial<Pick<Upload, 'ai_generated_image_url' | 'ai_description'>>
  ): Promise<boolean> {
    const updateData: any = { status }
    
    if (additionalData) {
      Object.assign(updateData, additionalData)
    }

    const { error } = await this.supabase
      .from('uploads')
      .update(updateData)
      .eq('id', uploadId)

    if (error) {
      console.error('Error updating upload status:', error)
      return false
    }

    return true
  }

  // Get upload by ID
  async getUpload(uploadId: string): Promise<Upload | null> {
    const { data, error } = await this.supabase
      .from('uploads')
      .select('*')
      .eq('id', uploadId)
      .single()

    if (error) {
      console.error('Error fetching upload:', error)
      return null
    }

    return data
  }
}

// Create singleton instances
export const creditsService = new CreditsService() // Client-side
export const serverCreditsService = new CreditsService(true) // Server-side
