/**
 * Client-side credits utilities
 * 
 * This file provides client-safe credit operations that don't require
 * server-side privileges. For operations that need elevated permissions,
 * use the API routes instead.
 */

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { UserCredits, CreditTransaction, Upload } from './credits'

export class ClientCreditsService {
  private supabase = createClientComponentClient()

  // Get user's current credits (client-safe)
  async getUserCredits(userId: string): Promise<UserCredits | null> {
    const { data, error } = await this.supabase
      .from('user_credits')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching user credits:', error)
      return null
    }

    return data
  }

  // Get user's credit transactions (client-safe)
  async getCreditTransactions(userId: string, limit: number = 10): Promise<CreditTransaction[]> {
    const { data, error } = await this.supabase
      .from('credit_transactions')
      .select(`
        *,
        uploads (
          id,
          filename,
          status,
          created_at
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching credit transactions:', error)
      return []
    }

    return data || []
  }

  // Get user's uploads (client-safe)
  async getUserUploads(userId: string, limit: number = 10): Promise<Upload[]> {
    const { data, error } = await this.supabase
      .from('uploads')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching user uploads:', error)
      return []
    }

    return data || []
  }

  // Check if user has enough credits (client-safe)
  async hasEnoughCredits(userId: string, requiredCredits: number = 1): Promise<boolean> {
    const credits = await this.getUserCredits(userId)
    return credits ? credits.credits_remaining >= requiredCredits : false
  }

  // Get transaction statistics (client-safe)
  async getTransactionStats(userId: string): Promise<{
    totalPurchased: number
    totalUsed: number
    totalTransactions: number
  }> {
    const { data, error } = await this.supabase
      .from('credit_transactions')
      .select('amount, transaction_type')
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching transaction stats:', error)
      return { totalPurchased: 0, totalUsed: 0, totalTransactions: 0 }
    }

    const stats = data.reduce(
      (acc, transaction) => {
        acc.totalTransactions++
        if (transaction.transaction_type === 'purchase') {
          acc.totalPurchased += transaction.amount
        } else if (transaction.transaction_type === 'usage') {
          acc.totalUsed += Math.abs(transaction.amount)
        }
        return acc
      },
      { totalPurchased: 0, totalUsed: 0, totalTransactions: 0 }
    )

    return stats
  }

  // Initialize user credits via API (client-safe)
  async initializeUserCredits(): Promise<boolean> {
    try {
      const response = await fetch('/api/credits/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to initialize credits')
      }

      const result = await response.json()
      return result.success
    } catch (error) {
      console.error('Error initializing credits:', error)
      return false
    }
  }
}

// Export singleton instance for client-side use
export const clientCreditsService = new ClientCreditsService()
