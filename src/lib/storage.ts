import { supabase } from './auth'

export interface UploadResult {
  path: string
  url: string
  error?: string
}

export interface FileMetadata {
  name: string
  size: number
  type: string
  lastModified: number
}

export class StorageService {
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  private static readonly ALLOWED_TYPES = [
    'image/jpeg',
    'image/png', 
    'image/webp',
    'application/pdf'
  ]

  /**
   * Upload a file to the uploads bucket
   */
  static async uploadFile(
    file: File,
    userId: string,
    options?: {
      folder?: string
      generatePath?: boolean
    }
  ): Promise<UploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file)
      if (!validation.valid) {
        return { path: '', url: '', error: validation.error }
      }

      // Generate secure file path
      const filePath = options?.generatePath 
        ? await this.generateSecureFilePath(userId, file.name)
        : `${userId}/${options?.folder || 'uploads'}/${Date.now()}-${file.name}`

      // Upload file
      const { data, error } = await supabase.storage
        .from('uploads')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        console.error('Upload error:', error)
        return { path: '', url: '', error: error.message }
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('uploads')
        .getPublicUrl(data.path)

      return {
        path: data.path,
        url: urlData.publicUrl,
        error: undefined
      }
    } catch (error) {
      console.error('Storage service error:', error)
      return { 
        path: '', 
        url: '', 
        error: error instanceof Error ? error.message : 'Upload failed' 
      }
    }
  }

  /**
   * Get a signed URL for a file (for private access)
   */
  static async getSignedUrl(
    bucket: string,
    path: string,
    expiresIn: number = 3600
  ): Promise<{ url: string; error?: string }> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .createSignedUrl(path, expiresIn)

      if (error) {
        return { url: '', error: error.message }
      }

      return { url: data.signedUrl }
    } catch (error) {
      return { 
        url: '', 
        error: error instanceof Error ? error.message : 'Failed to get signed URL' 
      }
    }
  }

  /**
   * Delete a file from storage
   */
  static async deleteFile(bucket: string, path: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([path])

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Delete failed' 
      }
    }
  }

  /**
   * List files in a user's folder
   */
  static async listUserFiles(
    bucket: string,
    userId: string,
    folder?: string
  ): Promise<{ files: any[]; error?: string }> {
    try {
      const path = folder ? `${userId}/${folder}` : userId

      const { data, error } = await supabase.storage
        .from(bucket)
        .list(path, {
          limit: 100,
          offset: 0,
          sortBy: { column: 'created_at', order: 'desc' }
        })

      if (error) {
        return { files: [], error: error.message }
      }

      return { files: data || [] }
    } catch (error) {
      return { 
        files: [], 
        error: error instanceof Error ? error.message : 'Failed to list files' 
      }
    }
  }

  /**
   * Save processed OCR result
   */
  static async saveProcessedResult(
    userId: string,
    originalFileName: string,
    result: any,
    type: 'ocr' | 'invoice' | 'translation' = 'ocr'
  ): Promise<UploadResult> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const fileName = `${type}-${originalFileName}-${timestamp}.json`
      const filePath = `${userId}/processed/${fileName}`

      const blob = new Blob([JSON.stringify(result, null, 2)], {
        type: 'application/json'
      })

      const { data, error } = await supabase.storage
        .from('processed')
        .upload(filePath, blob)

      if (error) {
        return { path: '', url: '', error: error.message }
      }

      const { data: urlData } = supabase.storage
        .from('processed')
        .getPublicUrl(data.path)

      return {
        path: data.path,
        url: urlData.publicUrl
      }
    } catch (error) {
      return { 
        path: '', 
        url: '', 
        error: error instanceof Error ? error.message : 'Failed to save result' 
      }
    }
  }

  /**
   * Validate uploaded file
   */
  private static validateFile(file: File): { valid: boolean; error?: string } {
    if (file.size > this.MAX_FILE_SIZE) {
      return { 
        valid: false, 
        error: `File too large. Maximum size is ${this.MAX_FILE_SIZE / 1024 / 1024}MB` 
      }
    }

    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return { 
        valid: false, 
        error: `Invalid file type. Allowed types: ${this.ALLOWED_TYPES.join(', ')}` 
      }
    }

    return { valid: true }
  }

  /**
   * Generate a secure file path using the database function
   */
  private static async generateSecureFilePath(userId: string, originalName: string): Promise<string> {
    try {
      const extension = originalName.split('.').pop() || 'jpg'
      
      const { data, error } = await supabase.rpc('generate_file_path', {
        user_id: userId,
        bucket_name: 'uploads',
        file_extension: extension
      })

      if (error || !data) {
        // Fallback to timestamp-based path
        return `${userId}/${Date.now()}-${originalName}`
      }

      return data
    } catch (error) {
      // Fallback to timestamp-based path
      return `${userId}/${Date.now()}-${originalName}`
    }
  }
}

export default StorageService