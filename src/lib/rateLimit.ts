import { RateLimitInfo } from '@/types'

interface RateLimitEntry {
  requests: number
  resetTime: number
}

class RateLimiter {
  private cache = new Map<string, RateLimitEntry>()
  private readonly limit: number
  private readonly windowMs: number

  constructor(limit: number = 60, windowMs: number = 60000) {
    this.limit = limit
    this.windowMs = windowMs
  }

  check(identifier: string): RateLimitInfo {
    const now = Date.now()
    const entry = this.cache.get(identifier)

    if (!entry || now > entry.resetTime) {
      // Reset window
      this.cache.set(identifier, {
        requests: 1,
        resetTime: now + this.windowMs
      })
      
      return {
        remaining: this.limit - 1,
        resetTime: now + this.windowMs,
        limit: this.limit
      }
    }

    if (entry.requests >= this.limit) {
      return {
        remaining: 0,
        resetTime: entry.resetTime,
        limit: this.limit
      }
    }

    entry.requests++
    
    return {
      remaining: this.limit - entry.requests,
      resetTime: entry.resetTime,
      limit: this.limit
    }
  }

  isAllowed(identifier: string): boolean {
    const info = this.check(identifier)
    return info.remaining >= 0
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.resetTime) {
        this.cache.delete(key)
      }
    }
  }
}

export const rateLimiter = new RateLimiter(60, 60000) // 60 requests per minute

export function getRateLimitHeaders(rateLimitInfo: RateLimitInfo): Record<string, string> {
  return {
    'X-RateLimit-Limit': rateLimitInfo.limit.toString(),
    'X-RateLimit-Remaining': rateLimitInfo.remaining.toString(),
    'X-RateLimit-Reset': rateLimitInfo.resetTime.toString(),
  }
}
