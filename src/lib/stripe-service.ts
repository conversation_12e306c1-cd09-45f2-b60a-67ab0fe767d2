import { loadStripe } from '@stripe/stripe-js'
import { creditPackages, CreditPackage } from '@/stripe-config'

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

export class StripeService {
  // Create checkout session for credit purchase
  async createCheckoutSession(packageId: string, userId: string) {
    const creditPackage = creditPackages.find(pkg => pkg.id === packageId)
    
    if (!creditPackage) {
      throw new Error('Invalid credit package')
    }

    const response = await fetch('/api/stripe/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        priceId: creditPackage.priceId,
        credits: creditPackage.credits,
        userId: userId,
        packageId: packageId
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(error || 'Failed to create checkout session')
    }

    const { sessionId } = await response.json()
    return sessionId
  }

  // Redirect to Stripe Checkout
  async redirectToCheckout(sessionId: string) {
    const stripe = await stripePromise
    
    if (!stripe) {
      throw new Error('Stripe failed to load')
    }

    const { error } = await stripe.redirectToCheckout({
      sessionId: sessionId,
    })

    if (error) {
      throw error
    }
  }

  // Purchase credits (combines create session and redirect)
  async purchaseCredits(packageId: string, userId: string) {
    try {
      const sessionId = await this.createCheckoutSession(packageId, userId)
      await this.redirectToCheckout(sessionId)
    } catch (error) {
      console.error('Error purchasing credits:', error)
      throw error
    }
  }

  // Get credit package by ID
  getCreditPackage(packageId: string): CreditPackage | undefined {
    return creditPackages.find(pkg => pkg.id === packageId)
  }

  // Get all credit packages
  getAllCreditPackages(): CreditPackage[] {
    return creditPackages
  }
}

// Create singleton instance
export const stripeService = new StripeService()

// React hook for Stripe operations
import { useState } from 'react'

export function useStripe() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const purchaseCredits = async (packageId: string, userId: string) => {
    try {
      setLoading(true)
      setError(null)
      await stripeService.purchaseCredits(packageId, userId)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to purchase credits')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    purchaseCredits,
    loading,
    error,
    clearError: () => setError(null)
  }
}
