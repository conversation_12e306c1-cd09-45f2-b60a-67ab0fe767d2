{"version": "1.0", "name": "ocr-clean", "description": "Fixes common OCR misreads and segments lines properly", "prompt": "You are an OCR text cleaning specialist. Clean and correct the following OCR output:\n\n{raw_text}\n\nCorrect common OCR errors:\n- Fix character substitutions (e.g., '0' for 'O', '1' for 'l', 'rn' for 'm')\n- Correct spacing issues\n- Fix punctuation placement\n- Ensure proper line segmentation\n- Preserve original formatting structure\n\nReturn the cleaned text maintaining the original line structure.", "parameters": [{"name": "raw_text", "type": "string", "required": true, "description": "Raw OCR text output to be cleaned"}], "output_format": "cleaned_text"}