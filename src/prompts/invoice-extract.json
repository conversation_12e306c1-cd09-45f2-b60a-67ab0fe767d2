{"version": "1.0", "name": "invoice-extract", "description": "Extracts structured invoice data from OCR text", "prompt": "Extract invoice information from the following OCR text. Parse and structure the data according to the specified format.\n\nOCR Text:\n{ocr_text}\n\nExtract the following fields:\n- Invoice Number\n- Date (normalize to YYYY-MM-DD)\n- Billing Name\n- Billing Address\n- Line Items (description, quantity, unit price, line total)\n- Subtotal\n- Tax Rate (%)\n- Total Amount\n- Currency\n\nReturn as JSON:\n{\n  \"invoiceNumber\": \"string\",\n  \"date\": \"YYYY-MM-DD\",\n  \"billingName\": \"string\",\n  \"billingAddress\": \"string\",\n  \"items\": [\n    {\n      \"description\": \"string\",\n      \"quantity\": number,\n      \"unitPrice\": number,\n      \"lineTotal\": number\n    }\n  ],\n  \"subtotal\": number,\n  \"taxRate\": number,\n  \"total\": number,\n  \"currency\": \"string\"\n}", "parameters": [{"name": "ocr_text", "type": "string", "required": true, "description": "OCR extracted text from invoice image"}], "output_format": "json"}