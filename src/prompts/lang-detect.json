{"version": "1.0", "name": "lang-detect", "description": "Detects language per line and normalizes dates/numbers", "prompt": "Analyze the following text and detect the language for each line. Also normalize any dates to YYYY-MM-DD format and extract numeric quantities with currency amounts.\n\nText:\n{text}\n\nFor each line, provide:\n1. Original text\n2. Detected language (th/en/zh)\n3. Confidence score (0-1)\n4. Normalized dates (if any)\n5. Extracted numbers/currency (if any)\n\nReturn as JSON array with format:\n[\n  {\n    \"line\": \"original text\",\n    \"language\": \"th|en|zh\",\n    \"confidence\": 0.95,\n    \"normalized_dates\": [\"2025-07-01\"],\n    \"extracted_numbers\": [{\"value\": 1500, \"unit\": \"฿\"}]\n  }\n]", "parameters": [{"name": "text", "type": "string", "required": true, "description": "Text to analyze for language detection"}], "output_format": "json"}