# Netlify Deployment Guide for OCR AI

This guide covers deploying your OCR AI application to Netlify with optimal configuration.

## 🚀 Quick Deploy

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/YOUR_USERNAME/ocr-ai)

## 📋 Prerequisites

1. **Netlify Account**: Sign up at [netlify.com](https://netlify.com)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **Environment Variables**: Prepare your environment variables (see below)

## 🔧 Environment Variables Setup

In your Netlify dashboard, go to **Site Settings > Environment Variables** and add:

```bash
# Required Variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
TOGETHER_API_KEY=your_together_ai_api_key
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Build Configuration
NODE_ENV=production
NODE_VERSION=18
NPM_CONFIG_LEGACY_PEER_DEPS=true
```

## 🏗️ Build Settings

### Automatic Configuration
The `netlify.toml` file in your repository will automatically configure:
- Build command: `npm run build`
- Publish directory: `.next`
- Node.js version: 18
- Next.js plugin integration

### Manual Configuration (if needed)
If you need to configure manually:

1. **Build Command**: `npm run build`
2. **Publish Directory**: `.next`
3. **Functions Directory**: `netlify/functions`

## 🔌 Netlify Plugins

The deployment uses these plugins:
- `@netlify/plugin-nextjs` - Optimizes Next.js for Netlify

## 🌐 Domain Configuration

### Custom Domain
1. Go to **Domain Settings** in your Netlify dashboard
2. Add your custom domain
3. Configure DNS settings as shown
4. Enable HTTPS (automatic with Netlify)

### Subdomain
Your app will be available at: `https://your-app-name.netlify.app`

## 🔒 Security Headers

The `netlify.toml` configures security headers:
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- XSS Protection

## 📊 Performance Optimizations

### Caching
- Static assets cached for 1 year
- Next.js optimizations enabled
- Image optimization configured

### Build Optimizations
- SWC minification enabled
- Console.log removed in production
- Package imports optimized

## 🚨 Troubleshooting

### Common Issues

1. **Build Fails**
   ```bash
   # Check Node.js version
   NODE_VERSION=18
   
   # Check dependencies
   npm ci --legacy-peer-deps
   ```

2. **Environment Variables Not Working**
   - Ensure variables are set in Netlify dashboard
   - Redeploy after adding variables
   - Check variable names match exactly

3. **API Routes Not Working**
   - Verify Next.js plugin is installed
   - Check `netlify.toml` configuration
   - Ensure proper redirects are set

4. **Database Connection Issues**
   - Verify Supabase URLs and keys
   - Check Supabase project is active
   - Verify service role key permissions

### Health Check
Visit `/netlify/functions/health` to check service status:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "supabase": true,
    "stripe": true,
    "together": true
  }
}
```

## 📈 Monitoring

### Netlify Analytics
Enable Netlify Analytics in your dashboard for:
- Page views and unique visitors
- Top pages and sources
- Performance metrics

### Error Monitoring
Consider adding:
- Sentry for error tracking
- LogRocket for user session recording
- Hotjar for user behavior analytics

## 🔄 CI/CD Pipeline

### Automatic Deployments
- **Production**: Deploys from `main` branch
- **Preview**: Deploys from pull requests
- **Branch Deploys**: Deploys from specific branches

### Deploy Hooks
Create deploy hooks for:
- Manual deployments
- Webhook integrations
- Scheduled deployments

## 🎯 Performance Tips

1. **Image Optimization**
   - Use Next.js Image component
   - Configure image domains in `next.config.js`
   - Use WebP/AVIF formats

2. **Bundle Analysis**
   ```bash
   # Analyze bundle size
   npm run build
   npx @next/bundle-analyzer
   ```

3. **Lighthouse Audits**
   - Enable automatic Lighthouse checks
   - Monitor Core Web Vitals
   - Optimize based on recommendations

## 🆘 Support

- **Netlify Support**: [docs.netlify.com](https://docs.netlify.com)
- **Next.js on Netlify**: [docs.netlify.com/frameworks/next-js](https://docs.netlify.com/frameworks/next-js/)
- **Community**: [community.netlify.com](https://community.netlify.com)

## 📚 Additional Resources

- [Netlify CLI](https://cli.netlify.com/) - Local development and deployment
- [Netlify Dev](https://www.netlify.com/products/dev/) - Local Netlify environment
- [Build Plugins](https://docs.netlify.com/configure-builds/build-plugins/) - Extend build process
