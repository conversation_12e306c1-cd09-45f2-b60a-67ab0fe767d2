# OpenRouter API key for translation & prompt chains
OPENROUTER_API_KEY=or-<your_openrouter_api_key>

# Together AI / llama-ocr credentials
TOGETHER_API_KEY=pk-...

# Supabase URL & anon/service key
SUPABASE_URL=https://gfgygdteyrumaxifgyqg.supabase.co
SUPABASE_SERVICE_ROLE_KEY=...

# n8n webhook URL
N8N_WEBHOOK_URL=...

# Next.js environment
NEXT_PUBLIC_SUPABASE_URL=https://gfgygdteyrumaxifgyqg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmZ3lnZHRleXJ1bWF4aWZneXFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNDE5MzAsImV4cCI6MjA2NjkxNzkzMH0.emXnfiAIw0KLujLPXoU02kEOSI6E7DWWhrMW1F2JUPQ

# Stripe Configuration
STRIPE_SECRET_KEY=sk_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_...
STRIPE_WEBHOOK_SECRET=whsec_...
