# 🔧 Vercel Environment Variables Setup

## Step 1: Go to Vercel Dashboard
Visit: https://vercel.com/roongorjs-projects/ocr-ai/settings/environment-variables

## Step 2: Add These Environment Variables

### Required for Production:

| Variable Name | Value | Environment |
|---------------|-------|-------------|
| `NEXT_PUBLIC_SUPABASE_URL` | `https://gfgygdteyrumaxifgyqg.supabase.co` | Production |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | `your_supabase_anon_key` | Production |
| `SUPABASE_SERVICE_ROLE_KEY` | `your_service_role_key` | Production |
| `TOGETHER_API_KEY` | `your_together_ai_key` | Production |
| `STRIPE_SECRET_KEY` | `your_stripe_secret_key` | Production |
| `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` | `your_stripe_public_key` | Production |
| `STRIPE_WEBHOOK_SECRET` | `your_stripe_webhook_secret` | Production |
| `NODE_ENV` | `production` | Production |
| `NEXT_PUBLIC_APP_URL` | `https://ocr-ai-six.vercel.app` | Production |

## Step 3: Supabase Auth Configuration

### Site URL:
```
https://ocr-ai-six.vercel.app
```

### Redirect URLs:
```
https://ocr-ai-six.vercel.app
https://ocr-ai-six.vercel.app/auth/callback
https://ocr-ai-six.vercel.app/dashboard
https://ocr-ai-six.vercel.app/upload
https://ocr-ai-six.vercel.app/pricing
https://ocr-ai-six.vercel.app/success
```

### Additional Redirect URLs (for testing):
```
http://localhost:3000
http://localhost:3000/auth/callback
http://localhost:3000/dashboard
```

## Step 4: Redeploy
After adding environment variables, run:
```bash
vercel --prod
```

## Step 5: Test Your Application
- Home: https://ocr-ai-six.vercel.app
- Upload: https://ocr-ai-six.vercel.app/upload
- Login: https://ocr-ai-six.vercel.app/login
- API Health: https://ocr-ai-six.vercel.app/api/health

## 🔍 Verification Checklist
- [ ] Supabase Site URL updated
- [ ] All redirect URLs added to Supabase
- [ ] Environment variables set in Vercel
- [ ] Application redeployed
- [ ] Authentication working
- [ ] API routes responding
- [ ] Upload functionality working
