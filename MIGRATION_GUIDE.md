# Supabase Migration Guide - Credits System

## Quick Setup Instructions

### 1. Apply Database Migration 📊

1. **Open Supabase Dashboard**
   - Go to: https://supabase.com/dashboard/project/gfgygdteyrumaxifgyqg
   - Navigate to **SQL Editor**

2. **Run the Migration**
   - Copy the entire content from `manual_migration_credits_system.sql`
   - Paste into the SQL Editor
   - Click **Run** to execute

3. **Verify Migration Success**
   - Check that no errors appeared
   - Run this verification query:
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name IN ('user_credits', 'uploads', 'credit_transactions');
   ```
   - Should return 3 rows

### 2. Update Environment Variables 🔧

Create a `.env.local` file in your project root with:

```bash
# Supabase
SUPABASE_URL=https://gfgygdteyrumaxifgyqg.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://gfgygdteyrumaxifgyqg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmZ3lnZHRleXJ1bWF4aWZneXFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNDE5MzAsImV4cCI6MjA2NjkxNzkzMH0.emXnfiAIw0KLujLPXoU02kEOSI6E7DWWhrMW1F2JUPQ
SUPABASE_SERVICE_ROLE_KEY=YOUR_SERVICE_ROLE_KEY_HERE

# Stripe (we already created products)
STRIPE_SECRET_KEY=YOUR_STRIPE_SECRET_KEY
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=YOUR_STRIPE_PUBLISHABLE_KEY
STRIPE_WEBHOOK_SECRET=YOUR_WEBHOOK_SECRET

# AI APIs
TOGETHER_API_KEY=YOUR_TOGETHER_API_KEY
OPENROUTER_API_KEY=YOUR_OPENROUTER_API_KEY
```

**⚠️ Important**: You need to get your `SUPABASE_SERVICE_ROLE_KEY` from:
- Supabase Dashboard → Settings → API → service_role key

### 3. Deploy Stripe Webhook 🪝

The Stripe webhook is already set up in `/supabase/functions/stripe-webhook/index.ts`

To deploy it:

```bash
# Install Supabase CLI if not installed
npm install -g supabase

# Login to Supabase
npx supabase login

# Link your project
npx supabase link --project-ref gfgygdteyrumaxifgyqg

# Deploy the webhook function
npx supabase functions deploy stripe-webhook
```

After deployment, configure Stripe webhook:
1. Go to Stripe Dashboard → Webhooks
2. Add endpoint: `https://gfgygdteyrumaxifgyqg.supabase.co/functions/v1/stripe-webhook`
3. Select events: `checkout.session.completed`
4. Copy webhook secret to your `.env.local`

### 4. Test the System 🧪

1. **Start your development server**:
   ```bash
   npm run dev
   ```

2. **Test user signup**:
   - Create a new account
   - Check if user gets 15 credits automatically

3. **Test credit purchase**:
   - Go to `/dashboard/credits`
   - Try purchasing credits with Stripe test cards

4. **Test OCR with credits**:
   - Upload an image for OCR
   - Verify credits are deducted

## What's Included in Migration

✅ **3 Tables Created**:
- `user_credits` - User credit balances
- `uploads` - OCR operation tracking
- `credit_transactions` - Complete audit trail

✅ **Security Features**:
- Row Level Security (RLS) enabled
- User data isolation policies
- Service role permissions

✅ **Database Functions**:
- `deduct_credits()` - Atomic credit deduction
- `add_credits()` - Credit addition with transactions
- `handle_new_user()` - Auto-create 15 credits on signup

✅ **Triggers**:
- Auto-credit creation on user signup
- Auto-update timestamps

✅ **Performance Indexes**:
- Optimized for user lookups
- Fast transaction queries

## Testing Queries

After migration, you can test with these SQL queries:

```sql
-- Check tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_credits', 'uploads', 'credit_transactions');

-- Check functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('deduct_credits', 'add_credits', 'handle_new_user');

-- Test credit creation (as service_role)
SELECT * FROM public.user_credits LIMIT 5;
```

## Troubleshooting

**❌ Permission Errors**:
- Make sure you're using the service_role key for server-side operations
- Check RLS policies are correctly set

**❌ Function Not Found**:
- Ensure all functions were created successfully
- Check function permissions with GRANT statements

**❌ Stripe Webhook Issues**:
- Verify webhook URL is correct
- Check webhook secret matches your environment
- Test with Stripe CLI: `stripe listen --forward-to localhost:54321/functions/v1/stripe-webhook`

## Production Checklist

- [ ] Migration applied successfully
- [ ] Environment variables configured
- [ ] Stripe webhook deployed and configured
- [ ] Test user signup (gets 15 credits)
- [ ] Test credit purchase
- [ ] Test OCR operation (deducts credits)
- [ ] RLS policies working (users see only their data)

Your credits system is now ready! 🎉
