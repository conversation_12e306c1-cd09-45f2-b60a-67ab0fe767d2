# 🔧 Netlify 404 Error Fix Guide

If you're seeing a 404 error on your Netlify deployment, follow these steps:

## ✅ Immediate Actions

### 1. Check Build Status
- Go to your Netlify dashboard
- Check if the latest deploy was successful
- Look for any build errors in the deploy log

### 2. Verify Environment Variables
Make sure these are set in Netlify dashboard → Site Settings → Environment Variables:
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
TOGETHER_API_KEY=your_together_ai_key
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_public_key
NODE_ENV=production
```

### 3. Force Redeploy
- In Netlify dashboard, go to Deploys
- Click "Trigger deploy" → "Deploy site"
- Wait for the new build to complete

## 🔍 Common Issues & Solutions

### Issue 1: Missing Plugin
**Symptoms**: 404 on all pages
**Solution**: 
1. Check that `@netlify/plugin-nextjs` is in `package.json`
2. Verify `netlify.toml` has the plugin configured
3. Redeploy

### Issue 2: Wrong Build Command
**Symptoms**: Build succeeds but pages don't work
**Solution**:
1. Check Build settings in Netlify dashboard:
   - Build command: `npm run build`
   - Publish directory: `.next`
2. Save and redeploy

### Issue 3: Next.js App Router Issues
**Symptoms**: Home page works, but routes like `/upload` return 404
**Solution**: The current configuration should handle this, but if it doesn't:

1. Check the `_redirects` file exists in `public/`
2. Verify `netlify.toml` configuration
3. Try manually adding this redirect in Netlify dashboard:
   ```
   /* /index.html 200
   ```

### Issue 4: API Routes Not Working
**Symptoms**: Frontend works, but `/api/*` routes return 404
**Solution**:
1. Verify Next.js plugin is properly configured
2. Check that API routes are in `src/app/api/` directory
3. Ensure environment variables are set

## 🚀 Quick Fix Commands

Run these locally to verify everything works:

```bash
# Test build
npm run build

# Test locally
npm run dev

# Check for TypeScript errors
npm run lint
```

## 📞 Emergency Debugging

If nothing else works:

1. **Check Browser Console**: Look for JavaScript errors
2. **Check Network Tab**: See which requests are failing
3. **Test Individual Routes**: 
   - Try `yoursite.netlify.app` (home page)
   - Try `yoursite.netlify.app/upload` (upload page)
   - Try `yoursite.netlify.app/api/health` (API endpoint)

## 🔧 Advanced Fixes

### If _redirects isn't working:
Add this to Netlify dashboard → Site Settings → Redirects:
```
/*    /index.html   200
```

### If Next.js plugin isn't working:
Temporarily add to `netlify.toml`:
```toml
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### If build is failing:
Check Node.js version in build log - should be 18.x:
```toml
[build.environment]
  NODE_VERSION = "18"
```

## ✅ Verification Checklist

After fixing, verify these work:
- [ ] Home page loads (`/`)
- [ ] Upload page loads (`/upload`)
- [ ] API health check works (`/api/health`)
- [ ] 404 page shows for invalid routes (`/nonexistent`)
- [ ] Authentication pages work (`/login`, `/signup`)

## 📞 Still Need Help?

1. Check Netlify deploy logs for specific error messages
2. Try the Netlify community forum
3. Compare your working local version with the deployed version

---

**Remember**: Most 404 issues are configuration problems, not code problems. If it works locally, it should work on Netlify with the right configuration!
