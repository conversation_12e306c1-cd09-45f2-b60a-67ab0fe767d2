# CLAUDE.md

## Standard Workflow

1. First think through the problem, read the codebase for relevant files, and write a plan to `tasks/todo.md`.
2. The plan should have a list of todo items that you can check off as you complete them.
3. Before you begin working, check in with me and I will verify the plan.
4. Then, begin working on the todo items, marking them as complete as you go.
5. At every step, provide a high-level explanation of what changes you made.
6. Make every task and code change as simple as possible. Avoid large or complex changes; each change should impact as little code as possible to maintain simplicity.
7. Finally, add a Review section to the `tasks/todo.md` file with a summary of the changes you made and any other relevant information.

## Common Commands
`bun install`     # Installs dependencies
`bun run lint `   # Runs the linting scripts
`bun test`        # Runs the test suite
---
Project Governance & Contribution Guidelines
---



## 1. Coding Standards

- **Language & Style**  
  - **TypeScript** for all application code.  
  - **React/Next.js** conventions for component structure.  
  - **TailwindCSS** utility classes—no custom CSS unless absolutely necessary.  
- **Linting & Formatting**  
  - **ESLint** with the Airbnb + TypeScript config.  
  - **Prettier** for code formatting.  
  - All code must pass `bun lint`  and `bun format` before merging.

## 2. Branching Strategy

- **`main`**  
  - Production-ready code only.  

  - Protected: requires PR review and passing CI checks.  
- **`develop`**  
  - Latest integrated features.  
  - Automatically deployed to staging.  
- **Feature Branches**  
  - Naming: `feature/<short-description>` (e.g. `feature/webhook-auth`).  
  - Created off `develop`.  
  - Prefix with Jira ticket or issue number when applicable.  
- **Release Branches**  
  - Named `release/<version>` (e.g. `release/1.0.0`).  
  - Created from `develop` when preparing for production.  
- **Hotfix Branches**  
  - Named `hotfix/<issue>` (e.g. `hotfix/login-bug`).  
  - Created from `main`, merged back into both `main` and `develop`.

## 3. Pull Request & Code Review

1. **PR Requirements**  
   - Must target `develop` (unless hotfix).  
   - Include a clear title, description, and link to related issue or ticket.  
   - List out key changes and any manual testing steps.  
2. **Review Process**  
   - At least **two reviewers** required.  
   - Reviewers check for:  
     - Correctness & edge cases  
     - Adherence to coding standards  
     - Security considerations  
     - Performance implications  
3. **CI/CD Checks**  
   - All tests must pass (`bun test`).  
   - Lint and type-check must pass.  
   - Build must succeed (`bun build`).

## 4. Contribution Guidelines

- **Issue Tracking**  
  - Use GitHub Issues.  
  - Provide reproducible steps, expected vs. actual behavior.  
- **Pull Requests**  
  - Assign yourself when you begin work.  
  - Keep PRs small and focused.  
- **Documentation**  
  - Update **CLAUDE.md**, **STATUS.md**, or **README.md** when behavior or API changes.  
  - For public-facing changes, update user-facing docs in `/docs`.

## 5. Security Best Practices

- **Secrets Management**  
  - All secrets in Supabase or environment variables.  
  - Never commit `.env` or API keys—use `.env.example`.  
- **Authentication & Authorization**  
  - Supabase Auth for email/password and OAuth.  
  - Row Level Security (RLS) policies on all tables.  
- **Input Validation**  
  - Sanitize all user inputs (files, text).  
  - Validate file types, size limits (e.g., max 10 MB).  
- **Dependency Audits**  
  - Regularly run `bun audit`.  
  - Patch or update vulnerable packages within 48 hours of disclosure.

This document outlines the configuration, capabilities, and usage guidelines for the AI Assistant ("Claude") powering the OCR‑SaaS platform.

---

## 1. Overview

### Current Model Version and Key Features

**Claude 4 Sonnet** is the latest iteration of Anthropic's AI assistant, designed for enhanced reasoning, analysis, and content generation. Key features include:

- **Advanced reasoning capabilities** for complex multi-step problem solving
- **Expanded context window** supporting extensive document analysis
- **Enhanced code generation and debugging** across multiple programming languages
- **Improved multimodal capabilities** for image analysis and OCR tasks
- **Refined safety-first design** with advanced constitutional AI principles
- **Better performance** on specialized tasks like invoice processing and translation

### Core Capabilities and Strengths

#### Text Processing & Analysis
- **OCR Text Cleaning**: Corrects common misreads (0→O, 1→l, rn→m) with higher accuracy
- **Language Detection**: Identifies Thai, English, and Chinese text with improved confidence scores
- **Content Extraction**: Enhanced structured data extraction from invoices and documents
- **Translation**: Context-aware translation preserving business terminology with better nuance

#### Code & Technical Tasks
- **Full-stack development** (Next.js, React, TypeScript, SQL) with improved code quality
- **API integration** (Supabase, Stripe, OpenRouter) with better error handling
- **Database design** and migration scripts with enhanced optimization
- **Error debugging** and performance optimization with deeper analysis

#### Document Processing
- **Invoice parsing** with more accurate structured JSON output
- **Template generation** for various document formats with better formatting
- **Data normalization** (dates, currencies, measurements) with improved precision
- **Quality validation** and confidence scoring with enhanced reliability

### Known Limitations and Restrictions

#### Technical Constraints
- **No real-time data access** - Cannot fetch live information
- **No file system access** - Cannot directly read/write files
- **No external API calls** - Cannot make HTTP requests during processing
- **Context window limits** - Large documents may need chunking (though improved from previous versions)

#### Processing Limitations
- **Image analysis** requires base64 encoding or URLs
- **Complex calculations** may have precision limitations (though improved)
- **Memory constraints** for very large datasets (with better handling)
- **Rate limiting** applies to API usage

---

## 2. Technical Specifications

### Context Window Size
- **Maximum tokens**: ~200,000 tokens (enhanced from previous versions)
- **Effective context**: ~180,000 tokens for optimal performance
- **Document size**: Up to ~600 pages of text per request (improved capacity)

### Supported File Formats and Data Types

#### Input Formats
```
Images: JPEG, PNG, WebP, GIF (with better processing)
Documents: PDF (via OCR), TXT, JSON, CSV
Code: All major programming languages (enhanced support)
Data: Structured (JSON, XML) and unstructured text
```

#### Output Formats
```
Text: Plain text, Markdown, HTML (improved formatting)
Data: JSON, CSV, XML (better structure)
Code: Any programming language (enhanced quality)
Templates: HTML/CSS for invoices and documents (improved design)
```

### API Rate Limits and Quotas

#### OpenRouter Integration
- **Rate limit**: 60 requests/minute per user
- **Token limits**: Varies by model and subscription
- **Concurrent requests**: Limited by API key tier
- **Enhanced throughput**: Better performance per request

#### Processing Quotas
- **OCR processing**: 100 documents/day (free tier)
- **Translation**: 10,000 characters/day (free tier)
- **Invoice extraction**: 50 invoices/day (free tier)

---

## 3. Best Practices

### Prompt Engineering Guidelines

#### Structure Your Prompts
```
1. Context: Provide clear background information
2. Task: Specify exactly what you want
3. Format: Define expected output format
4. Examples: Include sample inputs/outputs when possible
5. Constraints: Mention any limitations or requirements
```

#### Effective Prompt Patterns
```typescript
// Good: Specific and structured
const prompt = `
Extract invoice data from this OCR text:
${ocrText}

Return JSON with these exact fields:
- invoiceNumber (string)
- date (YYYY-MM-DD format)
- total (number)
- currency (string)
`

// Bad: Vague and unstructured
const prompt = `Process this invoice: ${ocrText}`
```

### Tips for Optimal Results

#### OCR Text Cleaning
```json
{
  "preprocessing": [
    "Remove extra whitespace",
    "Fix common character substitutions",
    "Preserve original line structure",
    "Maintain numeric formatting"
  ],
  "validation": [
    "Check confidence scores",
    "Validate extracted data types",
    "Cross-reference related fields"
  ]
}
```

#### Language Detection
```javascript
// Best practice: Provide context
const request = {
  text: ocrText,
  context: "invoice", // Helps with ambiguous text
  languages: ["th", "en", "zh"], // Limit search space
  confidence_threshold: 0.8 // Filter low-confidence results
}
```

#### Translation Guidelines
```javascript
// Preserve business context
const translationPrompt = `
Translate this Thai invoice text to English:
"${thaiText}"

Guidelines:
- Preserve all numbers and dates exactly
- Use standard business English terms
- Maintain currency symbols (฿, $)
- Keep measurement units unchanged
`
```

### Common Pitfalls to Avoid

#### ❌ Don't Do This
```javascript
// Vague prompts
"Fix this text"

// No validation
const result = await processOCR(text)
return result // No error checking

// Ignoring confidence scores
const translation = data.translatedText // May be low quality
```

#### ✅ Do This Instead
```javascript
// Specific prompts with context
"Clean OCR text by fixing character substitutions (0→O, 1→l) while preserving line structure"

// Proper validation
const result = await processOCR(text)
if (result.confidence < 0.8) {
  // Flag for manual review
  return { ...result, needsReview: true }
}

// Check confidence scores
if (translation.confidence > 0.9) {
  return translation.text
} else {
  return { text: translation.text, warning: "Low confidence translation" }
}
```

---

## 4. Use Cases

### Recommended Applications

#### Primary Use Cases
1. **Invoice Processing**: Extract structured data from scanned invoices
2. **Document Translation**: Multi-language business document translation
3. **OCR Cleanup**: Improve accuracy of raw OCR output
4. **Data Validation**: Verify and normalize extracted information

#### Advanced Applications
1. **Template Generation**: Create custom invoice templates
2. **Batch Processing**: Handle multiple documents efficiently
3. **Quality Assurance**: Automated accuracy checking
4. **Compliance Checking**: Validate document formats and content

### Sample Prompts and Outputs

#### OCR Text Cleaning
```javascript
// Input
const rawOCR = "lnv0ice #l23\nD4te: 2O25-O7-Ol\nT0tal: $l,5OO.OO"

// Prompt
const cleanPrompt = `
Clean this OCR text by fixing common character recognition errors:
${rawOCR}

Fix these substitutions:
- 0 → O (when it should be letter O)
- l → 1 (when it should be number 1)
- O → 0 (when it should be number 0)
`

// Output
"Invoice #123\nDate: 2025-07-01\nTotal: $1,500.00"
```

#### Invoice Data Extraction
```javascript
// Input
const invoiceText = `
INVOICE #INV-2025-001
Date: July 1, 2025
Bill To: ABC Company Ltd.
123 Business Street, Bangkok

Item: Web Development Services
Quantity: 1
Unit Price: $2,500.00
Total: $2,500.00

Subtotal: $2,500.00
Tax (7%): $175.00
Total Amount: $2,675.00
`

// Expected Output
{
  "invoiceNumber": "INV-2025-001",
  "date": "2025-07-01",
  "billingName": "ABC Company Ltd.",
  "billingAddress": "123 Business Street, Bangkok",
  "items": [
    {
      "description": "Web Development Services",
      "quantity": 1,
      "unitPrice": 2500.00,
      "lineTotal": 2500.00
    }
  ],
  "subtotal": 2500.00,
  "taxRate": 7,
  "total": 2675.00,
  "currency": "USD"
}
```

### Real-World Examples

#### Multi-Language Invoice Processing
```typescript
// Thai invoice with mixed languages
const thaiInvoice = `
ใบแจ้งหนี้ #TH-2025-001
วันที่: 1 กรกฎาคม 2568
บริษัท ABC จำกัด
123 ถนนธุรกิจ กรุงเทพฯ

รายการ: บริการพัฒนาเว็บไซต์
จำนวน: 1
ราคาต่อหน่วย: ฿85,000
รวม: ฿85,000

รวมย่อย: ฿85,000
ภาษี (7%): ฿5,950
รวมทั้งสิ้น: ฿90,950
`

// Processing workflow
const workflow = {
  step1: "OCR text cleaning",
  step2: "Language detection (Thai detected)",
  step3: "Invoice data extraction",
  step4: "Optional translation to English",
  step5: "Data validation and formatting"
}
```

---

## 5. Ethical Guidelines

### Content Policies

#### Acceptable Use
- ✅ Business document processing
- ✅ Educational content analysis
- ✅ Personal document organization
- ✅ Accessibility improvements (OCR for visually impaired)

#### Prohibited Use
- ❌ Processing confidential/classified documents without authorization
- ❌ Extracting personal information for unauthorized purposes
- ❌ Creating fake or fraudulent documents
- ❌ Processing copyrighted material without permission

### Safety Measures

#### Data Privacy
```typescript
// Implement data retention policies
const dataPolicy = {
  retention: "30 days for uploads, 90 days for processed results",
  encryption: "All data encrypted at rest and in transit",
  access: "User-specific access controls via RLS",
  deletion: "Automatic cleanup of expired files"
}
```

#### Error Handling
```typescript
// Safe error responses
const safeErrorHandling = {
  noPersonalInfo: "Never include personal data in error messages",
  genericMessages: "Use generic error descriptions",
  logging: "Log detailed errors server-side only",
  userFeedback: "Provide helpful but non-revealing feedback"
}
```

### Responsible AI Usage

#### Transparency
- Always disclose AI processing to users
- Provide confidence scores for all outputs
- Allow manual review of low-confidence results
- Maintain audit logs of AI decisions

#### Accuracy and Bias
```typescript
// Bias mitigation strategies
const biasReduction = {
  validation: "Cross-validate results across multiple models",
  diversity: "Test with diverse document types and languages",
  feedback: "Collect user feedback on accuracy",
  improvement: "Continuously update and retrain models"
}
```

#### User Control
- Users can review and edit all extracted data
- Provide options to disable AI features
- Allow export of original and processed data
- Implement user feedback mechanisms

---

## 6. Integration Guidelines

### API Integration Best Practices

#### Rate Limiting
```typescript
// Implement proper rate limiting
const rateLimitConfig = {
  requests: 60, // per minute
  burst: 10, // concurrent requests
  backoff: "exponential", // retry strategy
  timeout: 30000 // 30 second timeout
}
```

#### Error Recovery
```typescript
// Robust error handling
async function processWithRetry(data: any, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await processData(data)
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await delay(Math.pow(2, i) * 1000) // Exponential backoff
    }
  }
}
```

### Performance Optimization

#### Caching Strategy
```typescript
// Cache frequently used results
const cacheConfig = {
  ocrResults: "24 hours",
  translations: "7 days", 
  templates: "30 days",
  userPreferences: "indefinite"
}
```

#### Batch Processing
```typescript
// Efficient batch operations
const batchProcessor = {
  maxBatchSize: 10,
  concurrency: 3,
  timeout: 60000,
  retryFailedItems: true
}
```

---

## 7. Monitoring and Analytics

### Key Metrics

#### Performance Metrics
- Processing time per document
- OCR accuracy rates
- Translation quality scores
- User satisfaction ratings

#### Usage Analytics
- Documents processed per day
- Most common document types
- Language distribution
- Error rates by category

### Quality Assurance

#### Automated Testing
```typescript
// Test suite for AI components
const testSuite = {
  ocrAccuracy: "Test with known documents",
  translationQuality: "Compare with human translations", 
  extractionPrecision: "Validate structured data output",
  performanceBenchmarks: "Monitor processing times"
}
```

---

## 8. Claude 4 Enhancements

### Improved Capabilities
- **Enhanced reasoning**: Better logical deduction and problem-solving
- **Improved accuracy**: Higher precision in OCR correction and data extraction
- **Better context understanding**: More nuanced interpretation of business documents
- **Enhanced multilingual support**: Improved handling of mixed-language documents
- **Faster processing**: Optimized performance for common tasks

### Migration Notes
- **Backward compatibility**: All existing prompts and integrations continue to work
- **Performance improvements**: Expect better results with the same prompts
- **Enhanced error handling**: More detailed and helpful error messages
- **Improved confidence scoring**: More accurate confidence assessments

---

*Last updated: 2025‑01‑01*
*Powered by Claude 4 Sonnet*

*This documentation is maintained by the OCR-SaaS development team. For questions or updates, please refer to the project repository or contact the maintainers.*