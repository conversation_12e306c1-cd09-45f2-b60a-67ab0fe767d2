# RULES.md

This document establishes governance, coding standards, data policies, and community guidelines for the OCR‑SaaS project.

---

## 1. Governance & Roles

* **Project Owner**: Defines vision, approves major feature changes.
* **Maintainers**: Manage code review, release processes, and infrastructure.
* **Contributors**: Submit PRs, fix bugs, and propose enhancements.
* **Community**: Report issues, request features, and participate in discussions.

### 1.1 Decision Process

* Major architectural or tech‑stack changes require consensus from Owner + ≥2 Maintainers.
* Minor enhancements and bug fixes can be merged by any Maintainer after passing CI.

---

## 2. Coding Standards

* **Language**: TypeScript (frontend & Edge Functions) and SQL for DB scripts.
* **Style**: ESLint + Prettier with the shared configuration in `.eslintrc.js` and `.prettierrc`.
* **Commits**: Follow Conventional Commits (e.g., `feat:`, `fix:`, `chore:`).
* **PRs**: Require at least 1 approving review and passing tests.
* **Branching**: Use feature branches; `main` is always releasable.

---

## 3. Data & Security Policies

* **User Data**: PII (names, addresses) stored encrypted at rest in Postgres. Access only via authenticated API.
* **Storage Buckets**: `uploads/` and `generated/` enforce RLS; all URLs are signed and expire after 15 minutes.
* **Credentials**: No secret keys in repo. Use environment variables and Secret Manager.
* **Compliance**: GDPR & PDPA–compatible; allow data deletion on user request.

---

## 4. AI & Prompt Ethics

* **Fairness**: Do not train or prompt on biased data. Validate outputs for cultural or language bias.
* **Accuracy**: Log confidence scores; highlight low‑confidence segments for manual review.
* **Privacy**: OCR results only stored if user consents. Users can purge documents anytime.
* **Transparency**: Disclose AI‑powered steps in UI (e.g., “Processed by AI vX”).

---

## 5. Community Code of Conduct

* **Respect & Inclusion**: Be courteous. Harassment or hate speech is prohibited.
* **Collaboration**: Provide constructive feedback. Assume good faith.
* **Reporting**: Issues can be reported via GitHub Discussions or email to the Maintainers.
* **Enforcement**: Violations may result in blocking or removal from the project.

*Last updated: 2025‑07‑01*
