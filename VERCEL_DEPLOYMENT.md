# 🚀 Vercel Deployment Guide for OCR AI

This guide covers deploying your OCR AI application to Vercel with optimal configuration.

## 🎯 Why Vercel is Perfect for This Project

- **Built for Next.js**: Made by the same team
- **Zero Configuration**: Works out of the box
- **Edge Functions**: Perfect for API routes
- **Global CDN**: Blazing fast worldwide
- **Serverless**: Scales automatically

## 📋 Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **Environment Variables**: Prepare your environment variables

## 🚀 Quick Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/YOUR_USERNAME/ocr-ai)

## 🔧 Manual Deployment Steps

### 1. Install Vercel CLI (if not already installed)
```bash
npm install -g vercel
```

### 2. Login to Vercel
```bash
vercel login
```

### 3. Deploy from your project directory
```bash
# For development deployment
vercel

# For production deployment
vercel --prod
```

## 🌐 GitHub Integration (Recommended)

### 1. Connect Repository
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Vercel will auto-detect it's a Next.js project

### 2. Configure Build Settings
Vercel should automatically detect:
- **Framework**: Next.js
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Install Command**: `npm install`

## ⚙️ Environment Variables Setup

In your Vercel dashboard, go to **Project Settings > Environment Variables** and add:

### Required Variables
```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Together AI
TOGETHER_API_KEY=your_together_ai_api_key

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-app-name.vercel.app
```

### Environment Types
Set these variables for:
- ✅ **Production** (main branch)
- ✅ **Preview** (pull requests)
- ✅ **Development** (local development)

## 🔗 Custom Domain Configuration

### 1. Add Custom Domain
1. Go to **Project Settings > Domains**
2. Add your custom domain
3. Configure DNS settings as shown

### 2. SSL Certificate
- Vercel automatically provisions SSL certificates
- Your site will be available at `https://your-domain.com`

## 📊 Performance Optimizations

### Automatic Optimizations
Vercel provides these out of the box:
- **Image Optimization**: Next.js Image component
- **Code Splitting**: Automatic bundle optimization
- **Static Generation**: Pre-rendered pages
- **Edge Caching**: Global CDN

### Custom Optimizations
Our `vercel.json` includes:
- **Security Headers**: XSS protection, content type options
- **CORS Headers**: API route configuration
- **Function Duration**: 30s timeout for API routes

## 🔍 Monitoring & Analytics

### Vercel Analytics
Enable in dashboard for:
- Real User Monitoring (RUM)
- Web Vitals tracking
- Page performance metrics

### Vercel Speed Insights
Get detailed performance data:
- Core Web Vitals
- Performance scores
- User experience metrics

## 🚨 Troubleshooting

### Common Issues

1. **Build Fails**
   ```bash
   # Check locally first
   npm run build
   
   # If it works locally, check environment variables
   ```

2. **API Routes Not Working**
   - Verify environment variables are set
   - Check function logs in Vercel dashboard
   - Ensure API routes are in `src/app/api/`

3. **Images Not Loading**
   - Update `next.config.js` with your Vercel domain
   - Check image domains configuration

4. **Environment Variables Missing**
   - Go to Project Settings > Environment Variables
   - Ensure all required variables are set
   - Redeploy after adding variables

### Debug Tools

1. **Function Logs**
   - View in Vercel dashboard > Functions tab
   - Real-time logs for API routes

2. **Build Logs**
   - Check deployment logs for build errors
   - Available in Deployments tab

3. **Runtime Logs**
   - Monitor application performance
   - Track API response times

## 🔄 CI/CD Pipeline

### Automatic Deployments
- **Production**: Deploys from `main` branch
- **Preview**: Deploys from pull requests
- **Branch Deploys**: Deploys from feature branches

### Deploy Hooks
Create webhooks for:
- Content management systems
- External service integrations
- Scheduled deployments

## 🎯 Performance Tips

### 1. Image Optimization
```javascript
// Use Next.js Image component
import Image from 'next/image'

<Image
  src="/your-image.jpg"
  alt="Description"
  width={500}
  height={300}
  priority // For above-the-fold images
/>
```

### 2. API Route Optimization
```javascript
// Use edge runtime for faster cold starts
export const runtime = 'edge'

export async function GET() {
  // Your API logic
}
```

### 3. Static Generation
```javascript
// Use static generation where possible
export async function generateStaticParams() {
  // Generate static paths
}
```

## 📈 Scaling Considerations

### Function Limits
- **Execution Time**: 10s (Hobby), 60s (Pro), 900s (Enterprise)
- **Memory**: 1024MB (configurable)
- **Payload Size**: 4.5MB

### Database Connections
- Use connection pooling with Supabase
- Consider edge functions for low latency

## 🛡️ Security Best Practices

### Environment Variables
- Never commit secrets to repository
- Use different keys for production/development
- Rotate keys regularly

### API Security
- Implement rate limiting
- Validate all inputs
- Use HTTPS only

## 📞 Support Resources

- **Vercel Documentation**: [vercel.com/docs](https://vercel.com/docs)
- **Next.js on Vercel**: [vercel.com/docs/frameworks/nextjs](https://vercel.com/docs/frameworks/nextjs)
- **Community**: [github.com/vercel/vercel/discussions](https://github.com/vercel/vercel/discussions)

## ✅ Deployment Checklist

Before going live:
- [ ] All environment variables set
- [ ] Custom domain configured (if applicable)
- [ ] SSL certificate active
- [ ] API routes tested
- [ ] Image optimization configured
- [ ] Analytics enabled
- [ ] Performance tested
- [ ] Error monitoring set up

---

## 🎉 You're Ready!

With Vercel, your OCR AI application will have:
- ⚡ **Lightning Fast Performance**
- 🌍 **Global Edge Network**
- 🔒 **Enterprise Security**
- 📊 **Built-in Analytics**
- 🚀 **Zero Downtime Deployments**

Happy deploying! 🚀
