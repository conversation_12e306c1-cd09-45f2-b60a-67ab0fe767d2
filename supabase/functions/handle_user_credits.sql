-- Function to automatically create user credits on signup
CREATE OR R<PERSON><PERSON>CE FUNCTION handle_new_user() 
R<PERSON>UR<PERSON> trigger AS $$
BEGIN
  INSERT INTO public.user_credits (user_id)
  VALUES (new.id);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY definer;

-- Trigger to create user credits on signup
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE handle_new_user();

