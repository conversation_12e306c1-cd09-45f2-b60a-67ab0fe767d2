/*
  # Storage Buckets for OCR Application

  1. New Storage Buckets
    - `uploads`: For storing uploaded images before OCR processing
    - `processed`: For storing processed OCR results and generated documents
    - `templates`: For storing invoice templates and generated PDFs

  2. Security
    - Enable RLS on all buckets
    - Users can only access their own files
    - Authenticated users can upload to uploads bucket
    - Processed files are read-only for users
    - Templates are publicly readable but only admin writable

  3. File Policies
    - Upload size limits and file type restrictions
    - Automatic cleanup of temporary files
    - Secure file naming conventions
*/

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'uploads',
    'uploads',
    false,
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'application/pdf']
  ),
  (
    'processed',
    'processed',
    false,
    52428800, -- 50MB limit for processed files
    ARRAY['application/json', 'text/plain', 'application/pdf', 'text/html']
  ),
  (
    'templates',
    'templates',
    true, -- Public for template previews
    5242880, -- 5MB limit
    ARRAY['text/html', 'text/css', 'application/json', 'image/png', 'image/jpeg']
  )
ON CONFLICT (id) DO NOTHING;

-- Storage policies for uploads bucket
CREATE POLICY "Users can upload their own files"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view their own uploaded files"
ON storage.objects
FOR SELECT
TO authenticated
USING (
  bucket_id = 'uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own uploaded files"
ON storage.objects
FOR DELETE
TO authenticated
USING (
  bucket_id = 'uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Storage policies for processed bucket
CREATE POLICY "Users can view their own processed files"
ON storage.objects
FOR SELECT
TO authenticated
USING (
  bucket_id = 'processed' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "System can create processed files"
ON storage.objects
FOR INSERT
TO service_role
WITH CHECK (bucket_id = 'processed');

CREATE POLICY "Users can delete their own processed files"
ON storage.objects
FOR DELETE
TO authenticated
USING (
  bucket_id = 'processed' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Storage policies for templates bucket
CREATE POLICY "Anyone can view templates"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'templates');

CREATE POLICY "Only service role can manage templates"
ON storage.objects
FOR ALL
TO service_role
USING (bucket_id = 'templates')
WITH CHECK (bucket_id = 'templates');

-- Create a function to generate secure file paths
CREATE OR REPLACE FUNCTION generate_file_path(
  user_id uuid,
  bucket_name text,
  file_extension text DEFAULT 'jpg'
)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  file_path text;
  timestamp_str text;
  random_suffix text;
BEGIN
  -- Generate timestamp string
  timestamp_str := to_char(now(), 'YYYY/MM/DD');
  
  -- Generate random suffix
  random_suffix := encode(gen_random_bytes(8), 'hex');
  
  -- Construct file path: user_id/YYYY/MM/DD/random_suffix.extension
  file_path := user_id::text || '/' || timestamp_str || '/' || random_suffix || '.' || file_extension;
  
  RETURN file_path;
END;
$$;

-- Create a function to clean up old files (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_files()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Delete old uploaded files (older than 30 days)
  DELETE FROM storage.objects
  WHERE bucket_id = 'uploads'
    AND created_at < now() - interval '30 days';
    
  -- Delete old processed files (older than 90 days)
  DELETE FROM storage.objects
  WHERE bucket_id = 'processed'
    AND created_at < now() - interval '90 days';
END;
$$;

-- Create a scheduled job to run cleanup (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('cleanup-old-files', '0 2 * * *', 'SELECT cleanup_old_files();');